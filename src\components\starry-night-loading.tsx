'use client';

import Image from 'next/image';
import React, { useEffect, useState } from 'react';

// 星星 SVG 的内容，用作动画中的基本形状。
const starSvgContent = `
<svg viewBox="0 0 10 10" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
        d="M5.00007 0C4.79379 0 4.59596 0.0770941 4.45009 0.214325C4.30423 0.351555 4.22229 0.53768 4.22229 0.731754V3.26825C4.22229 3.46232 4.30423 3.64843 4.45009 3.78566C4.59596 3.92289 4.79379 4 5.00007 4C5.20635 4 5.40417 3.92289 5.55003 3.78566C5.69589 3.64843 5.77785 3.46232 5.77785 3.26825V0.731754C5.77763 0.537743 5.69561 0.351736 5.54979 0.21455C5.40398 0.0773639 5.20628 0.0002039 5.00007 0Z"
        fill="currentColor"></path>
    <path
        d="M5.00007 6C4.79379 6 4.59596 6.07711 4.45009 6.21437C4.30423 6.35162 4.22229 6.53779 4.22229 6.7319V9.2681C4.22229 9.46221 4.30423 9.64838 4.45009 9.78563C4.59596 9.92289 4.79379 10 5.00007 10C5.20635 10 5.40417 9.92289 5.55003 9.78563C5.69589 9.64838 5.77785 9.46221 5.77785 9.2681V6.7319C5.77763 6.53785 5.69561 6.35181 5.54979 6.21459C5.40398 6.07738 5.20628 6.0002 5.00007 6Z"
        fill="currentColor"></path>
    <path
        d="M3.2681 4.22266H0.731896C0.537785 4.22266 0.351624 4.3046 0.214366 4.45046C0.0771091 4.59632 0 4.79415 0 5.00043C0 5.20671 0.0771091 5.40455 0.214366 5.55041C0.351624 5.69627 0.537785 5.77821 0.731896 5.77821H3.2681C3.46221 5.77821 3.64838 5.69627 3.78563 5.55041C3.92289 5.40455 4 5.20671 4 5.00043C4 4.79415 3.92289 4.59632 3.78563 4.45046C3.64838 4.3046 3.46221 4.22266 3.2681 4.22266Z"
        fill="currentColor"></path>
    <path
        d="M9.26988 4.22266H6.73214C6.53803 4.22266 6.35187 4.3046 6.21461 4.45046C6.07735 4.59632 6.00024 4.79415 6.00024 5.00043C6.00024 5.20671 6.07735 5.40455 6.21461 5.55041C6.35187 5.69627 6.53803 5.77821 6.73214 5.77821H9.26835C9.46246 5.77821 9.64862 5.69627 9.78588 5.55041C9.92314 5.40455 10.0002 5.20671 10.0002 5.00043C10.0002 4.79415 9.92314 4.59632 9.78588 4.45046C9.64862 4.3046 9.46246 4.22266 9.26835 4.22266H9.26988Z"
        fill="currentColor"></path>
</svg>`;

/**
 * 单个星星的状态接口。
 */
interface Star {
  id: number;
  style: React.CSSProperties;
  svgDataUrl: string;
}

/**
 * StarryNightLoading 组件的 Props。
 */
interface StarryNightLoadingProps {
  // 星星的颜色
  starColor?: string;
  // 星星的数量
  starCount?: number;
  // 自定义 CSS 类名
  className?: string;
}

/**
 * 一个显示满天星闪烁效果的加载动画组件。
 */
const StarryNightLoading: React.FC<StarryNightLoadingProps> = ({
  starColor = 'white',
  starCount = 30,
  className = '',
}) => {
  const [stars, setStars] = useState<Star[]>([]);

  useEffect(() => {
    // 使用指定颜色对 SVG 内容进行编码，SVG 中的 fill="currentColor" 会被替换。
    const coloredSvgContent = starSvgContent.replace(
      /<svg (.*?)>/,
      `<svg $1 style="color: ${starColor};">`
    );
    // 将着色后的 SVG 转换为 base64 Data URL
    const svgDataUrl = `data:image/svg+xml;base64,${btoa(coloredSvgContent)}`;

    const newStars: Star[] = [];
    for (let i = 0; i < starCount; i++) {
      const size = Math.random() * 15 + 8; // 星星尺寸在 8px 到 23px 之间
      const duration = Math.random() * 3 + 2; // 动画时长在 2s 到 5s 之间
      const delay = Math.random() * 3; // 动画延迟在 0s 到 3s 之间

      newStars.push({
        id: i,
        svgDataUrl: svgDataUrl,
        style: {
          position: 'absolute',
          top: `${Math.random() * 100}%`,
          insetInlineStart: `${Math.random() * 100}%`,
          width: `${size}px`,
          height: `${size}px`,
          animationName: 'sparkle-effect', // 需要在全局 CSS 中定义
          animationDuration: `${duration}s`,
          animationDelay: `${delay}s`,
          animationIterationCount: 'infinite',
          animationTimingFunction: 'cubic-bezier(0.4, 0, 0.6, 1)',
          opacity: 0, // 初始透明，由动画控制显现
        },
      });
    }
    setStars(newStars);
  }, [starColor, starCount]);

  return (
    <div
      className={`absolute inset-0 overflow-hidden pointer-events-none ${className}`}
    >
      {stars.map(star => (
        <Image
          key={star.id}
          src={star.svgDataUrl}
          alt='' // 装饰性图片，无需 alt 文本
          style={star.style}
          role='presentation' // 从可访问性树中隐藏
          width={parseInt(star.style.width as string)}
          height={parseInt(star.style.height as string)}
          unoptimized={true} // SVG data URL 不需要优化
        />
      ))}
    </div>
  );
};

export default StarryNightLoading;
