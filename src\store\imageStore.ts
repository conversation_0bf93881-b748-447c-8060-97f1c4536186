import { enableMapSet } from 'immer';
import { temporal, type TemporalState } from 'zundo';
import { create } from 'zustand';
import { immer } from 'zustand/middleware/immer';
import { imageStorage } from '@/storage/indexeddbStorage';
import { resizeImage, type ResizeMode } from '@/lib/imageUtils/imageResize';
import {
  convertImageFormat,
  getImageFormatFromFile,
  getImageFormatFromUrl,
  updateFileNameExtension,
  type SupportedFormat,
} from '@/lib/imageUtils/imageConvert';
import { generatePreviewUrl } from '@/lib/imageUtils/imageProcessingPipeline';

/**
 * 安全地将字符串转换为支持的格式
 */
function toSupportedFormat(format: string): SupportedFormat {
  const supportedFormats: SupportedFormat[] = [
    'png',
    'jpg',
    'jpeg',
    'webp',
    'bmp',
    'xbm',
    'xpm',
  ];
  return supportedFormats.includes(format as SupportedFormat)
    ? (format as SupportedFormat)
    : 'png';
}

// 启用 Immer 对 Map 和 Set 的支持
enableMapSet();

// 1. 定义图片状态的类型
export interface ImageState {
  id: string; // 使用 nanoid 或 crypto.randomUUID() 生成的唯一ID
  file?: File; // 原始文件（可选，URL加载的图片没有File对象）
  previewUrl: string; // 用于预览的URL
  name: string;
  width: number;
  height: number;
  size: number;
  status:
    | 'original'
    | 'bg-removed'
    | 'compressed'
    | 'locked'
    | 'bg-remove-failed';
  timestamp: number; // 图片创建时间戳，用于排序

  // 从原 BackgroundRemover 组件迁移过来的属性
  processedUrl?: string | null;
  backgroundColor: string;
  backgroundImageUrl?: string;
  backgroundImageId?: string; // 自定义背景图片的ID，用于刷新后恢复选中状态
  isBlurEnabled: boolean;
  blurAmount: number;

  // 橡皮擦相关状态
  isEraseMode: boolean; // 是否处于橡皮擦模式
  isRestoreMode: boolean; // 是否处于恢复模式
  eraseBrushSize: number; // 橡皮擦画笔大小
  eraseHistory: ImageData[]; // 橡皮擦操作历史
  currentEraseCanvasData?: ImageData | null; // 当前橡皮擦画布的状态数据
  eraseOperationCount: number; // 橡皮擦操作计数器，用于触发历史记录

  // 尺寸调整相关状态
  targetWidth?: number; // 目标宽度（像素）
  targetHeight?: number; // 目标高度（像素）
  resizeMode: 'fit' | 'fill' | 'stretch'; // 调整模式：适应、填充、拉伸
  originalWidth?: number; // 原始宽度（用于重置）
  originalHeight?: number; // 原始高度（用于重置）
  resizedUrl?: string | null; // 调整尺寸后的图片URL

  // 预览相关状态
  compositePreviewUrl?: string | null; // 合成预览URL（包含背景和所有效果）

  // 格式转换相关状态
  originalFormat?: string; // 原始图片格式（如 'png', 'jpg', 'webp' 等）
  convertedFormat?: string; // 转换后的格式
  convertedUrl?: string | null; // 格式转换后的图片URL

  // 压缩相关状态
  originalSize?: number; // 原始文件大小（字节）
  compressedSize?: number; // 压缩后文件大小（字节）
  compressedUrl?: string | null; // 压缩后的图片URL
  compressionLevel?: 'original' | 'light' | 'medium' | 'deep'; // 压缩程度
  customCompressionSize?: number; // 自定义压缩大小
  customCompressionUnit?: 'KB' | 'MB'; // 自定义压缩单位
}

// 2. 定义整个 store 的 state 类型
export interface StoreState {
  images: Map<string, ImageState>;
  selectedImageIds: Set<string>;
  // 持久化相关状态
  isStorageInitialized: boolean;
  isStorageLoading: boolean;
  isSaving: boolean;
  autoSaveEnabled: boolean;
  // 防抖保存的定时器映射
  saveTimers: Map<string, NodeJS.Timeout>;
}

// 3. 定义操作函数的类型
export interface StoreActions {
  // 原有的图片操作
  addImage: (image: ImageState) => void;
  updateImage: (id: string, updates: Partial<Omit<ImageState, 'id'>>) => void;
  removeImage: (id: string) => void;
  clearImages: () => void;
  toggleImageSelection: (id: string) => void;
  selectAllImages: () => void;
  clearSelection: () => void;

  // 预览URL相关操作
  updateImagePreviewUrl: (imageId: string) => Promise<void>;
  batchUpdatePreviewUrls: (imageIds: string[]) => Promise<void>;

  // 尺寸调整相关操作
  batchResizeImages: (
    imageIds: string[],
    targetWidth: number,
    targetHeight: number,
    mode?: 'fit' | 'fill' | 'stretch',
    onProgress?: (
      current: number,
      total: number,
      currentImageId?: string
    ) => void
  ) => Promise<void>;
  batchResizeImagesByRatio: (
    imageIds: string[],
    aspectRatio: number,
    mode?: 'fit' | 'fill' | 'stretch',
    onProgress?: (
      current: number,
      total: number,
      currentImageId?: string
    ) => void
  ) => Promise<void>;
  batchResizeImagesByScale: (
    imageIds: string[],
    scale: number,
    onProgress?: (
      current: number,
      total: number,
      currentImageId?: string
    ) => void
  ) => Promise<void>;
  resetImageResize: (imageId: string) => void;
  batchResetImageResize: (
    imageIds: string[],
    onProgress?: (
      current: number,
      total: number,
      currentImageId?: string
    ) => void
  ) => Promise<void>;
  clearAllResizes: () => void;

  // 批量重命名相关操作
  batchRenameImages: (
    imageIds: string[],
    prefix: string,
    startNumber: number,
    numberStep: number,
    onProgress?: (
      current: number,
      total: number,
      currentImageId?: string
    ) => void
  ) => Promise<void>;
  updateImageName: (imageId: string, newName: string) => void;

  // 批量格式转换相关操作
  batchConvertImages: (
    imageIds: string[],
    targetFormat: string,
    onProgress?: (
      current: number,
      total: number,
      currentImageId?: string
    ) => void
  ) => Promise<void>;
  resetImageFormat: (imageId: string) => void;
  clearAllConverts: () => void;

  // 图片压缩相关操作
  compressImage: (
    imageId: string,
    level?: 'original' | 'light' | 'medium' | 'deep',
    customSize?: number,
    customUnit?: 'KB' | 'MB'
  ) => Promise<void>;

  batchCompressImages: (
    imageIds: string[],
    level?: 'original' | 'light' | 'medium' | 'deep',
    customSize?: number,
    customUnit?: 'KB' | 'MB',
    onProgress?: (
      current: number,
      total: number,
      currentImageId?: string
    ) => void | Promise<void>
  ) => Promise<void>;

  // 批量操作
  performBatchUpdate: (
    updates: Array<{ imageId: string; updates: Partial<ImageState> }>
  ) => void;

  // 持久化相关操作
  initializeStorage: () => Promise<void>;
  saveImageToPersistentStorage: (imageId: string) => Promise<void>;
  saveAllImagesToPersistentStorage: () => Promise<void>;
  loadImagesFromPersistentStorage: () => Promise<void>;
  deleteImageFromPersistentStorage: (imageId: string) => Promise<void>;
  cleanupOldImages: (keepCount?: number) => Promise<number>;
  setAutoSave: (enabled: boolean) => void;
}

// 导出这个组合类型，以便在组件中断言
export type FullStoreWithTemporal = StoreState &
  StoreActions & {
    temporal: TemporalState<Pick<StoreState, 'images'>>;
  };

// 4. 导出集成了中间件的 store
// 我们不再手动指定泛型，让 TS 从中间件的组合中自行推断最终类型
export const useImageStore = create(
  temporal(
    immer<StoreState & StoreActions>((set, get) => ({
      // 状态初始化
      images: new Map(),
      selectedImageIds: new Set(),
      isStorageInitialized: false,
      isStorageLoading: false,
      isSaving: false,
      autoSaveEnabled: true,
      saveTimers: new Map(),

      // 原有的图片操作
      addImage: image => {
        set(state => {
          // 如果图片有文件对象，检测并记录原始格式
          if (image.file && !image.originalFormat) {
            image.originalFormat = getImageFormatFromFile(image.file);
          } else if (!image.originalFormat) {
            // 如果没有文件对象，从URL中推断格式
            image.originalFormat = getImageFormatFromUrl(image.previewUrl);
          }

          state.images.set(image.id, image);
        });

        // 自动保存新添加的图片（使用防抖）
        const currentState = get();
        if (currentState.autoSaveEnabled && currentState.isStorageInitialized) {
          // 清除之前的定时器
          const existingTimer = currentState.saveTimers.get(image.id);
          if (existingTimer) {
            clearTimeout(existingTimer);
          }

          // 设置新的定时器
          const timer = setTimeout(() => {
            currentState.saveImageToPersistentStorage(image.id).catch(error => {
              console.error('自动保存图片失败:', error);
            });
            // 清除定时器引用
            set(state => {
              state.saveTimers.delete(image.id);
            });
          }, 1000); // 1秒后保存，避免频繁操作

          // 保存定时器引用
          set(state => {
            state.saveTimers.set(image.id, timer);
          });
        }
      },

      updateImage: (id, updates) => {
        set(state => {
          const image = state.images.get(id);
          if (image) {
            Object.assign(image, updates);
          }
        });

        // 获取更新后的图片状态，检查是否处于橡皮擦模式
        const currentState = get();
        const updatedImage = currentState.images.get(id);
        const isInEraserMode =
          updatedImage?.isEraseMode || updatedImage?.isRestoreMode;

        // 如果不在橡皮擦模式，则正常自动保存（使用防抖）
        if (
          !isInEraserMode &&
          currentState.autoSaveEnabled &&
          currentState.isStorageInitialized
        ) {
          // 清除之前的定时器
          const existingTimer = currentState.saveTimers.get(id);
          if (existingTimer) {
            clearTimeout(existingTimer);
          }

          // 设置新的定时器
          const timer = setTimeout(() => {
            currentState.saveImageToPersistentStorage(id).catch(error => {
              console.error('自动保存图片更新失败:', error);
            });
            // 清除定时器引用
            set(state => {
              state.saveTimers.delete(id);
            });
          }, 2000); // 2秒后保存，避免编辑时频繁保存

          // 保存定时器引用
          set(state => {
            state.saveTimers.set(id, timer);
          });
        }
      },

      removeImage: id => {
        set(state => {
          state.images.delete(id);
        });

        // 从持久化存储中删除
        const currentState = get();
        if (currentState.isStorageInitialized) {
          currentState.deleteImageFromPersistentStorage(id).catch(error => {
            console.error('删除持久化图片失败:', error);
          });
        }
      },

      clearImages: () => {
        set(state => {
          state.images.clear();
          // 同时清空选中列表
          state.selectedImageIds.clear();
        });

        // 注意：这里不自动清理持久化存储，可能需要用户手动确认
      },

      toggleImageSelection: id => {
        set(state => {
          if (state.selectedImageIds.has(id)) {
            state.selectedImageIds.delete(id);
          } else {
            state.selectedImageIds.add(id);
          }
        });

        // 保存当前选中的图片ID（如果是单选）
        const currentState = get();
        if (
          currentState.isStorageInitialized &&
          currentState.selectedImageIds.size <= 1
        ) {
          const selectedId =
            currentState.selectedImageIds.size === 1
              ? Array.from(currentState.selectedImageIds)[0]
              : null;

          imageStorage.saveCurrentSelectedImageId(selectedId).catch(error => {
            console.error('保存选中图片ID失败:', error);
          });
        }
      },

      selectAllImages: () => {
        set(state => {
          const allIds = Array.from(state.images.keys());
          state.selectedImageIds = new Set(allIds);
        });
      },

      clearSelection: () => {
        set(state => {
          state.selectedImageIds.clear();
        });

        // 清除选中状态时也保存到持久化存储
        const currentState = get();
        if (currentState.isStorageInitialized) {
          imageStorage.saveCurrentSelectedImageId(null).catch(error => {
            console.error('保存清空选中状态失败:', error);
          });
        }
      },

      // 更新图片的预览URL和真实大小
      updateImagePreviewUrl: async (imageId: string) => {
        const image = get().images.get(imageId);
        if (!image) return;

        try {
          // 检查是否需要处理
          const needsProcessing =
            (image.backgroundColor &&
              image.backgroundColor !== 'transparent') ||
            image.backgroundImageUrl ||
            (image.targetWidth && image.targetHeight) ||
            image.convertedFormat;

          if (!needsProcessing) {
            // 如果不需要处理，直接使用原始URL和大小
            set(state => {
              const img = state.images.get(imageId);
              if (img) {
                img.compositePreviewUrl = img.processedUrl || img.previewUrl;
                // 恢复原始大小
                if (img.originalSize) {
                  img.size = img.originalSize;
                }
              }
            });
            return;
          }

          // 构建完整的处理配置，包括格式转换
          const config = {
            backgroundColor: image.backgroundColor,
            backgroundImageUrl: image.backgroundImageUrl,
            targetWidth: image.targetWidth,
            targetHeight: image.targetHeight,
            resizeMode: image.resizeMode || 'fit',
            outputFormat: toSupportedFormat(
              image.convertedFormat || image.originalFormat || 'png'
            ),
            quality: 0.9, // 预览使用高质量
          };

          // 使用完整的处理管道计算真实的合成大小
          const { processImagePipeline } = await import(
            '../lib/imageUtils/imageProcessingPipeline'
          );
          const result = await processImagePipeline(
            image.previewUrl,
            image.processedUrl || null,
            config
          );

          set(state => {
            const img = state.images.get(imageId);
            if (img) {
              img.compositePreviewUrl = result.dataUrl;
              // 更新为真实的合成后大小
              img.size = result.size;
            }
          });
        } catch (error) {
          console.error(`更新图片 ${imageId} 预览URL失败:`, error);
          // 回退到简单的预览URL生成
          try {
            const previewUrl = await generatePreviewUrl(
              image.previewUrl,
              image.processedUrl || null,
              {
                backgroundColor: image.backgroundColor,
                backgroundImageUrl: image.backgroundImageUrl,
                targetWidth: image.targetWidth,
                targetHeight: image.targetHeight,
                resizeMode: image.resizeMode,
              }
            );

            set(state => {
              const img = state.images.get(imageId);
              if (img) {
                img.compositePreviewUrl = previewUrl;
              }
            });
          } catch (fallbackError) {
            console.error(`预览URL回退也失败:`, fallbackError);
          }
        }
      },

      // 批量更新预览URL
      batchUpdatePreviewUrls: async (imageIds: string[]) => {
        const promises = imageIds.map(imageId =>
          get().updateImagePreviewUrl(imageId)
        );
        await Promise.all(promises);
      },

      // 尺寸调整相关操作实现
      batchResizeImages: async (
        imageIds: string[],
        targetWidth: number,
        targetHeight: number,
        mode: ResizeMode = 'fit',
        onProgress?: (
          current: number,
          total: number,
          currentImageId?: string
        ) => void | Promise<void>
      ) => {
        const total = imageIds.length;
        let completed = 0;

        for (let i = 0; i < imageIds.length; i++) {
          const imageId = imageIds[i];
          const currentState = get();
          const image = currentState.images.get(imageId);

          if (!image) {
            console.warn(`图片不存在，跳过: ${imageId}`);
            completed++;
            if (onProgress) {
              onProgress(completed, total, imageId);
            }
            continue;
          }

          // 跳过锁定的图片
          if (image.status === 'locked') {
            completed++;
            if (onProgress) {
              onProgress(completed, total, imageId);
            }
            continue;
          }

          try {
            // 更新图片状态为处理中
            set(state => {
              const img = state.images.get(imageId);
              if (img) {
                // 保存原始尺寸（如果还没有保存的话）
                if (!img.originalWidth || !img.originalHeight) {
                  img.originalWidth = img.width;
                  img.originalHeight = img.height;
                }

                img.targetWidth = targetWidth;
                img.targetHeight = targetHeight;
                img.resizeMode = mode;
              }
            });

            // 确定要处理的图片URL
            let sourceUrl = image.processedUrl || image.previewUrl;

            // 如果是背景去除后的图片，优先使用处理后的URL
            if (image.status === 'bg-removed' && image.processedUrl) {
              sourceUrl = image.processedUrl;
            }

            // 确定输出格式
            const outputFormat =
              image.convertedFormat || image.originalFormat || 'png';
            const safeFormat = toSupportedFormat(outputFormat);

            // 调用尺寸调整函数
            const resizeResult = await resizeImage(
              sourceUrl,
              targetWidth,
              targetHeight,
              mode,
              0.9, // 高质量
              safeFormat // 使用安全的格式
            );

            // 更新图片状态
            set(state => {
              const img = state.images.get(imageId);
              if (img) {
                // 保存原始尺寸（如果还没有保存的话）
                if (!img.originalWidth || !img.originalHeight) {
                  img.originalWidth = img.width;
                  img.originalHeight = img.height;
                }
                // 注意：不再重新设置 originalSize，它应该在上传时就已经设置好了

                // 更新当前尺寸为目标尺寸
                img.width = targetWidth;
                img.height = targetHeight;
                img.targetWidth = targetWidth;
                img.targetHeight = targetHeight;
                img.resizeMode = mode;
                img.resizedUrl = resizeResult.dataUrl;

                // 注意：文件大小将由 updateImagePreviewUrl 通过完整处理管道计算
              }
            });

            // 异步更新预览URL（包含背景合成）
            get()
              .updateImagePreviewUrl(imageId)
              .catch(error => {
                console.error(`更新图片 ${imageId} 预览URL失败:`, error);
              });

            completed++;
          } catch (error) {
            console.error(`图片 ${imageId} 尺寸调整失败:`, error);

            // 更新状态为失败
            set(state => {
              const img = state.images.get(imageId);
              if (img) {
                // 错误处理已在外层 catch 中处理
              }
            });

            completed++;
          }

          // 更新进度
          if (onProgress) {
            await onProgress(completed, total, imageId);
          }

          // 添加小延迟避免浏览器阻塞
          if (i < imageIds.length - 1) {
            await new Promise(resolve => setTimeout(resolve, 10));
          }
        }
      },

      // 按宽高比批量调整图片尺寸
      batchResizeImagesByRatio: async (
        imageIds: string[],
        aspectRatio: number,
        mode: ResizeMode = 'fit',
        onProgress?: (
          current: number,
          total: number,
          currentImageId?: string
        ) => void | Promise<void>
      ) => {
        const total = imageIds.length;
        let completed = 0;

        // 逐个处理图片
        for (let i = 0; i < imageIds.length; i++) {
          const imageId = imageIds[i];
          const image = get().images.get(imageId);

          if (!image) {
            console.warn(`图片 ${imageId} 不存在，跳过处理`);
            completed++;
            continue;
          }

          // 跳过锁定的图片
          if (image.status === 'locked') {
            completed++;
            if (onProgress) {
              onProgress(completed, total, imageId);
            }
            continue;
          }

          try {
            // 更新图片状态为处理中
            set(state => {
              const img = state.images.get(imageId);
              if (img) {
                // 保存原始尺寸（如果还没有保存的话）
                if (!img.originalWidth || !img.originalHeight) {
                  img.originalWidth = img.width;
                  img.originalHeight = img.height;
                }
              }
            });

            // 根据原始图片尺寸和目标宽高比计算新尺寸
            const originalWidth = image.originalWidth || image.width;
            const originalHeight = image.originalHeight || image.height;

            let targetWidth: number;
            let targetHeight: number;

            // 统一策略：按最大边为基准，确保调整后的图片不会比原图更大
            // 计算两种可能的尺寸，选择不超出原始尺寸的那个
            const widthBasedHeight = Math.round(originalWidth / aspectRatio);
            const heightBasedWidth = Math.round(originalHeight * aspectRatio);

            if (widthBasedHeight <= originalHeight) {
              // 以宽度为基准的计算结果不超出原始高度，使用这个
              targetWidth = originalWidth;
              targetHeight = widthBasedHeight;
            } else {
              // 以高度为基准的计算结果
              targetWidth = heightBasedWidth;
              targetHeight = originalHeight;
            }

            // 确定要处理的图片URL
            let sourceUrl = image.processedUrl || image.previewUrl;

            // 如果是背景去除后的图片，优先使用处理后的URL
            if (image.status === 'bg-removed' && image.processedUrl) {
              sourceUrl = image.processedUrl;
            }

            // 确定输出格式
            const outputFormat =
              image.convertedFormat || image.originalFormat || 'png';
            const safeFormat = toSupportedFormat(outputFormat);

            // 调用尺寸调整函数
            const resizeResult = await resizeImage(
              sourceUrl,
              targetWidth,
              targetHeight,
              mode,
              0.9, // 高质量
              safeFormat // 使用安全的格式
            );

            // 更新图片状态
            set(state => {
              const img = state.images.get(imageId);
              if (img) {
                // 保存原始尺寸（如果还没有保存的话）
                if (!img.originalWidth || !img.originalHeight) {
                  img.originalWidth = img.width;
                  img.originalHeight = img.height;
                }
                // 注意：不再重新设置 originalSize，它应该在上传时就已经设置好了

                // 更新当前尺寸为目标尺寸
                img.width = targetWidth;
                img.height = targetHeight;
                img.targetWidth = targetWidth;
                img.targetHeight = targetHeight;
                img.resizeMode = mode;
                img.resizedUrl = resizeResult.dataUrl;

                // 注意：文件大小将由 updateImagePreviewUrl 通过完整处理管道计算
              }
            });

            // 异步更新预览URL（包含背景合成）
            get()
              .updateImagePreviewUrl(imageId)
              .catch(error => {
                console.error(`更新图片 ${imageId} 预览URL失败:`, error);
              });

            completed++;
          } catch (error) {
            console.error(`图片 ${imageId} 宽高比调整失败:`, error);

            // 更新状态为失败
            set(state => {
              const img = state.images.get(imageId);
              if (img) {
                // 错误处理已在外层 catch 中处理
              }
            });

            completed++;
          }

          // 更新进度
          if (onProgress) {
            await onProgress(completed, total, imageId);
          }

          // 添加小延迟避免浏览器阻塞
          if (i < imageIds.length - 1) {
            await new Promise(resolve => setTimeout(resolve, 10));
          }
        }
      },

      // 按比例缩放批量调整图片尺寸
      batchResizeImagesByScale: async (
        imageIds: string[],
        scale: number,
        onProgress?: (
          current: number,
          total: number,
          currentImageId?: string
        ) => void | Promise<void>
      ) => {
        const total = imageIds.length;
        let completed = 0;

        // 逐个处理图片
        for (let i = 0; i < imageIds.length; i++) {
          const imageId = imageIds[i];
          const image = get().images.get(imageId);

          if (!image) {
            console.warn(`图片 ${imageId} 不存在，跳过处理`);
            completed++;
            continue;
          }

          // 跳过锁定的图片
          if (image.status === 'locked') {
            completed++;
            if (onProgress) {
              onProgress(completed, total, imageId);
            }
            continue;
          }

          try {
            // 更新图片状态为处理中
            set(state => {
              const img = state.images.get(imageId);
              if (img) {
                // 保存原始尺寸（如果还没有保存的话）
                if (!img.originalWidth || !img.originalHeight) {
                  img.originalWidth = img.width;
                  img.originalHeight = img.height;
                }
              }
            });

            // 根据原始图片尺寸和缩放比例计算新尺寸
            const originalWidth = image.originalWidth || image.width;
            const originalHeight = image.originalHeight || image.height;

            const targetWidth = Math.round(originalWidth * scale);
            const targetHeight = Math.round(originalHeight * scale);

            // 确定要处理的图片URL
            let sourceUrl = image.processedUrl || image.previewUrl;

            // 如果是背景去除后的图片，优先使用处理后的URL
            if (image.status === 'bg-removed' && image.processedUrl) {
              sourceUrl = image.processedUrl;
            }

            // 确定输出格式
            const outputFormat =
              image.convertedFormat || image.originalFormat || 'png';
            const safeFormat = toSupportedFormat(outputFormat);

            // 调用尺寸调整函数
            const resizeResult = await resizeImage(
              sourceUrl,
              targetWidth,
              targetHeight,
              'fit', // 比例缩放使用 fit 模式
              0.9, // 高质量
              safeFormat // 使用安全的格式
            );

            // 更新图片状态
            set(state => {
              const img = state.images.get(imageId);
              if (img) {
                // 保存原始尺寸（如果还没有保存的话）
                if (!img.originalWidth || !img.originalHeight) {
                  img.originalWidth = img.width;
                  img.originalHeight = img.height;
                }
                // 注意：不再重新设置 originalSize，它应该在上传时就已经设置好了

                // 更新当前尺寸为目标尺寸
                img.width = targetWidth;
                img.height = targetHeight;
                img.targetWidth = targetWidth;
                img.targetHeight = targetHeight;
                img.resizeMode = 'fit';
                img.resizedUrl = resizeResult.dataUrl;

                // 注意：文件大小将由 updateImagePreviewUrl 通过完整处理管道计算
              }
            });

            // 异步更新预览URL（包含背景合成）
            get()
              .updateImagePreviewUrl(imageId)
              .catch(error => {
                console.error(`更新图片 ${imageId} 预览URL失败:`, error);
              });

            completed++;
          } catch (error) {
            console.error(`图片 ${imageId} 比例缩放失败:`, error);

            // 更新状态为失败
            set(state => {
              const img = state.images.get(imageId);
              if (img) {
                // 错误处理已在外层 catch 中处理
              }
            });

            completed++;
          }

          // 更新进度
          if (onProgress) {
            await onProgress(completed, total, imageId);
          }

          // 添加小延迟避免浏览器阻塞
          if (i < imageIds.length - 1) {
            await new Promise(resolve => setTimeout(resolve, 10));
          }
        }
      },

      resetImageResize: (imageId: string) => {
        set(state => {
          const image = state.images.get(imageId);
          if (image) {
            // 恢复原始尺寸
            if (image.originalWidth && image.originalHeight) {
              image.width = image.originalWidth;
              image.height = image.originalHeight;
            }

            // 恢复原始文件大小
            if (image.originalSize) {
              image.size = image.originalSize;
            }

            // 重置尺寸调整相关状态
            image.targetWidth = undefined;
            image.targetHeight = undefined;
            image.resizeMode = 'fit';
            image.resizedUrl = null;
          }
        });
      },

      batchResetImageResize: async (
        imageIds: string[],
        onProgress?: (
          current: number,
          total: number,
          currentImageId?: string
        ) => void | Promise<void>
      ) => {
        const total = imageIds.length;
        let completed = 0;

        // 逐个处理图片
        for (let i = 0; i < imageIds.length; i++) {
          const imageId = imageIds[i];
          const image = get().images.get(imageId);

          if (!image) {
            console.warn(`图片 ${imageId} 不存在，跳过处理`);
            completed++;
            continue;
          }

          // 跳过锁定的图片
          if (image.status === 'locked') {
            completed++;
            if (onProgress) {
              await onProgress(completed, total, imageId);
            }
            continue;
          }

          try {
            // 重置图片尺寸
            set(state => {
              const img = state.images.get(imageId);
              if (img) {
                // 恢复原始尺寸
                if (img.originalWidth && img.originalHeight) {
                  img.width = img.originalWidth;
                  img.height = img.originalHeight;
                }

                // 恢复原始文件大小
                if (img.originalSize) {
                  img.size = img.originalSize;
                }

                // 重置尺寸调整相关状态
                img.targetWidth = undefined;
                img.targetHeight = undefined;
                img.resizeMode = 'fit';
                img.resizedUrl = null;
              }
            });

            completed++;
            if (onProgress) {
              await onProgress(completed, total, imageId);
            }

            // 添加小延迟避免浏览器阻塞
            if (i < imageIds.length - 1) {
              await new Promise(resolve => setTimeout(resolve, 10));
            }
          } catch (error) {
            console.error(`批量重置中图片 ${imageId} 处理失败:`, error);
            completed++;
            if (onProgress) {
              await onProgress(completed, total, imageId);
            }
          }
        }
      },

      clearAllResizes: () => {
        set(state => {
          state.images.forEach(image => {
            // 恢复原始尺寸
            if (image.originalWidth && image.originalHeight) {
              image.width = image.originalWidth;
              image.height = image.originalHeight;
            }

            // 恢复原始文件大小
            if (image.originalSize) {
              image.size = image.originalSize;
            }

            // 重置尺寸调整相关状态
            image.targetWidth = undefined;
            image.targetHeight = undefined;
            image.resizeMode = 'fit';
            image.resizedUrl = null;
          });
        });
      },

      // 持久化相关操作
      initializeStorage: async () => {
        try {
          set(state => {
            state.isStorageLoading = true;
          });

          await imageStorage.init();

          set(state => {
            state.isStorageInitialized = true;
            state.isStorageLoading = false;
          });
        } catch (error) {
          console.error('图片存储初始化失败:', error);
          set(state => {
            state.isStorageLoading = false;
          });
          throw error;
        }
      },

      saveImageToPersistentStorage: async (imageId: string) => {
        const currentState = get();
        if (!currentState.isStorageInitialized) {
          throw new Error('存储未初始化');
        }

        const image = currentState.images.get(imageId);
        if (!image) {
          throw new Error(`图片不存在: ${imageId}`);
        }

        try {
          set(state => {
            state.isSaving = true;
          });

          await imageStorage.saveImage(image);
        } catch (error) {
          console.error(`保存图片失败: ${imageId}`, error);
          throw error;
        } finally {
          set(state => {
            state.isSaving = false;
          });
        }
      },

      saveAllImagesToPersistentStorage: async () => {
        const currentState = get();
        if (!currentState.isStorageInitialized) {
          throw new Error('存储未初始化');
        }

        try {
          set(state => {
            state.isSaving = true;
          });

          const images = Array.from(currentState.images.values());

          for (const image of images) {
            try {
              await imageStorage.saveImage(image);
            } catch (error) {
              console.error(`批量保存失败: ${image.id}`, error);
            }
          }
        } finally {
          set(state => {
            state.isSaving = false;
          });
        }
      },

      loadImagesFromPersistentStorage: async () => {
        const currentState = get();
        if (!currentState.isStorageInitialized) {
          throw new Error('存储未初始化');
        }

        try {
          set(state => {
            state.isStorageLoading = true;
          });

          const allMetadata = await imageStorage.getAllImageMetadata();

          // 暂停历史记录跟踪，避免加载过程产生历史记录
          useImageStore.temporal.getState().pause();

          try {
            let loadedCount = 0;

            for (const metadata of allMetadata) {
              try {
                const image = await imageStorage.loadImage(metadata.id);
                if (image) {
                  set(state => {
                    state.images.set(image.id, image);
                  });
                  loadedCount++;
                } else {
                  console.warn(`加载图片失败，数据为空: ${metadata.id}`);
                }
              } catch (error) {
                console.error(`加载图片失败: ${metadata.id}`, error);
              }
            }

            // 恢复选中状态
            if (loadedCount > 0) {
              try {
                const savedSelectedId =
                  await imageStorage.getCurrentSelectedImageId();
                if (savedSelectedId && get().images.has(savedSelectedId)) {
                  // 恢复之前选中的图片
                  set(state => {
                    state.selectedImageIds.clear();
                    state.selectedImageIds.add(savedSelectedId);
                  });
                } else if (get().images.size > 0) {
                  // 如果没有保存的选中状态或图片不存在，选中第一张图片
                  const firstImageId = Array.from(get().images.keys())[0];
                  set(state => {
                    state.selectedImageIds.clear();
                    state.selectedImageIds.add(firstImageId);
                  });
                }
              } catch (error) {
                console.error('恢复选中状态失败:', error);
                // 如果恢复失败，默认选中第一张图片
                if (get().images.size > 0) {
                  const firstImageId = Array.from(get().images.keys())[0];
                  set(state => {
                    state.selectedImageIds.clear();
                    state.selectedImageIds.add(firstImageId);
                  });
                }
              }
            }
          } finally {
            // 恢复历史记录跟踪
            useImageStore.temporal.getState().resume();
          }
        } finally {
          set(state => {
            state.isStorageLoading = false;
          });
        }
      },

      deleteImageFromPersistentStorage: async (imageId: string) => {
        const currentState = get();
        if (!currentState.isStorageInitialized) {
          throw new Error('存储未初始化');
        }

        try {
          await imageStorage.deleteImage(imageId);
        } catch (error) {
          console.error(`删除持久化图片失败: ${imageId}`, error);
          throw error;
        }
      },

      cleanupOldImages: async (keepCount = 10) => {
        const currentState = get();
        if (!currentState.isStorageInitialized) {
          throw new Error('存储未初始化');
        }

        try {
          const deletedCount = await imageStorage.cleanupOldImages(keepCount);

          return deletedCount;
        } catch (error) {
          console.error('清理旧图片失败:', error);
          throw error;
        }
      },

      // 批量重命名图片
      batchRenameImages: async (
        imageIds: string[],
        prefix: string,
        startNumber: number,
        numberStep: number,
        onProgress?: (
          current: number,
          total: number,
          currentImageId?: string
        ) => void | Promise<void>
      ) => {
        const total = imageIds.length;

        // 批量更新所有图片名称（单个 set 调用，创建单条历史记录）
        set(state => {
          let current = 0;

          imageIds.forEach(imageId => {
            current++;

            // 获取图片信息
            const image = state.images.get(imageId);
            if (!image) {
              console.warn(`图片不存在，跳过重命名: ${imageId}`);
              return;
            }

            // 计算当前图片的序号
            const currentNumber = startNumber + (current - 1) * numberStep;

            // 确定文件扩展名
            const format =
              image.convertedFormat || image.originalFormat || 'png';
            const extension = format === 'jpg' ? 'jpg' : format;

            // 生成新的文件名
            const newName = prefix
              ? `${prefix}(${currentNumber}).${extension}`
              : `image(${currentNumber}).${extension}`;

            // 更新图片名称
            image.name = newName;
          });
        });

        // 调用进度回调
        if (onProgress) {
          for (let i = 0; i < imageIds.length; i++) {
            await onProgress(i + 1, total, imageIds[i]);

            // 添加小延迟避免浏览器阻塞
            if (i < total - 1) {
              await new Promise(resolve => setTimeout(resolve, 10));
            }
          }
        }
      },

      // 更新单张图片名称
      updateImageName: (imageId: string, newName: string) => {
        set(state => {
          const image = state.images.get(imageId);
          if (image) {
            image.name = newName;
          }
        });
      },

      setAutoSave: (enabled: boolean) => {
        set(state => {
          state.autoSaveEnabled = enabled;
        });
      },

      // 批量格式转换相关操作实现
      batchConvertImages: async (
        imageIds: string[],
        targetFormat: string,
        onProgress?: (
          current: number,
          total: number,
          currentImageId?: string
        ) => void | Promise<void>
      ) => {
        const total = imageIds.length;
        let completed = 0;

        for (let i = 0; i < imageIds.length; i++) {
          const imageId = imageIds[i];
          const currentState = get();
          const image = currentState.images.get(imageId);

          if (!image) {
            console.warn(`图片不存在，跳过: ${imageId}`);
            completed++;
            if (onProgress) {
              onProgress(completed, total, imageId);
            }
            continue;
          }

          // 跳过锁定的图片
          if (image.status === 'locked') {
            completed++;
            if (onProgress) {
              onProgress(completed, total, imageId);
            }
            continue;
          }

          try {
            // 如果是恢复原始格式
            if (targetFormat === 'original') {
              // 重置为原始格式
              set(state => {
                const img = state.images.get(imageId);
                if (img) {
                  img.convertedFormat = undefined;
                  img.convertedUrl = null;

                  // 如果有原始格式信息，更新文件名
                  if (img.originalFormat) {
                    img.name = updateFileNameExtension(
                      img.name,
                      img.originalFormat
                    );
                  }
                }
              });
            } else {
              // 转换为指定格式

              // 确定要转换的图片URL
              let sourceUrl =
                image.processedUrl || image.resizedUrl || image.previewUrl;

              // 如果是背景去除后的图片，优先使用处理后的URL
              if (image.status === 'bg-removed' && image.processedUrl) {
                sourceUrl = image.processedUrl;
              }

              // 执行格式转换
              const convertResult = await convertImageFormat(
                sourceUrl,
                targetFormat as SupportedFormat,
                {
                  quality: 0.9,
                  keepTransparency: true,
                }
              );

              // 更新图片状态
              set(state => {
                const img = state.images.get(imageId);
                if (img) {
                  // 注意：不再重新设置 originalSize，它应该在上传时就已经设置好了

                  img.convertedFormat = targetFormat;
                  img.convertedUrl = convertResult.dataUrl;

                  // 注意：文件大小将由 updateImagePreviewUrl 通过完整处理管道计算

                  // 更新文件名扩展名
                  img.name = updateFileNameExtension(img.name, targetFormat);
                }
              });

              // 异步更新预览URL和真实大小
              get()
                .updateImagePreviewUrl(imageId)
                .catch(error => {
                  console.error(`更新图片 ${imageId} 预览URL失败:`, error);
                });
            }

            completed++;
          } catch (error) {
            console.error(`图片 ${imageId} 格式转换失败:`, error);

            // 更新状态为失败
            set(state => {
              const img = state.images.get(imageId);
              if (img) {
                // 错误处理已在外层 catch 中处理
              }
            });

            completed++;
          }

          // 更新进度
          if (onProgress) {
            await onProgress(completed, total, imageId);
          }

          // 添加小延迟避免浏览器阻塞
          if (i < imageIds.length - 1) {
            await new Promise(resolve => setTimeout(resolve, 10));
          }
        }
      },

      resetImageFormat: (imageId: string) => {
        set(state => {
          const image = state.images.get(imageId);
          if (image) {
            // 重置格式转换相关状态
            image.convertedFormat = undefined;
            image.convertedUrl = null;

            // 恢复原始文件大小
            if (image.originalSize) {
              image.size = image.originalSize;
            }

            // 如果有原始格式信息，恢复文件名
            if (image.originalFormat) {
              image.name = updateFileNameExtension(
                image.name,
                image.originalFormat
              );
            }
          }
        });
      },

      clearAllConverts: () => {
        set(state => {
          state.images.forEach(image => {
            image.convertedFormat = undefined;
            image.convertedUrl = null;

            // 恢复原始文件大小
            if (image.originalSize) {
              image.size = image.originalSize;
            }

            // 如果有原始格式信息，恢复文件名
            if (image.originalFormat) {
              image.name = updateFileNameExtension(
                image.name,
                image.originalFormat
              );
            }
          });
        });
      },

      // 压缩单张图片
      compressImage: async (
        imageId: string,
        level?: 'original' | 'light' | 'medium' | 'deep',
        customSize?: number,
        customUnit?: 'KB' | 'MB'
      ) => {
        const image = get().images.get(imageId);
        if (!image) {
          console.warn(`图片 ${imageId} 不存在`);
          return;
        }

        // 跳过锁定的图片
        if (image.status === 'locked') {
          return;
        }

        try {
          // 动态导入压缩工具
          const { compressImage } = await import(
            '../lib/imageUtils/imageCompress'
          );

          // 注意：不再重新设置 originalSize，它应该在上传时就已经设置好了
          // 如果由于某种原因 originalSize 不存在，使用当前大小作为回退
          if (!image.originalSize) {
            set(state => {
              const img = state.images.get(imageId);
              if (img) {
                img.originalSize = img.size;
                console.warn(
                  `图片 ${imageId} 的 originalSize 不存在，使用当前大小作为回退: ${img.size}`
                );
              }
            });
          }

          // 如果是恢复原始
          if (level === 'original') {
            set(state => {
              const img = state.images.get(imageId);
              if (img) {
                img.compressedUrl = null;
                img.compressedSize = undefined;
                img.compressionLevel = undefined;
                img.customCompressionSize = undefined;
                img.customCompressionUnit = undefined;
                img.size = img.originalSize || img.size;
              }
            });
            return;
          }

          // 确定要压缩的图片URL
          const sourceUrl =
            image.convertedUrl ||
            image.resizedUrl ||
            image.processedUrl ||
            image.previewUrl;

          // 确定输出格式
          const outputFormat =
            image.convertedFormat || image.originalFormat || 'png';
          const safeFormat = toSupportedFormat(outputFormat);

          // 执行压缩
          const compressionSettings = level
            ? { level }
            : { customSize, customUnit };

          const result = await compressImage(
            sourceUrl,
            compressionSettings,
            image.originalSize || image.size,
            safeFormat
          );

          // 更新图片状态
          set(state => {
            const img = state.images.get(imageId);
            if (img) {
              img.compressedUrl = result.compressedUrl;
              img.compressedSize = result.compressedSize;
              img.size = result.compressedSize;
              img.compressionLevel = level;
              img.customCompressionSize = customSize;
              img.customCompressionUnit = customUnit;
            }
          });
        } catch (error) {
          console.error(`图片 ${imageId} 压缩失败:`, error);
          throw error;
        }
      },

      // 批量压缩图片
      batchCompressImages: async (
        imageIds: string[],
        level?: 'original' | 'light' | 'medium' | 'deep',
        customSize?: number,
        customUnit?: 'KB' | 'MB',
        onProgress?: (
          current: number,
          total: number,
          currentImageId?: string
        ) => void | Promise<void>
      ) => {
        const total = imageIds.length;
        let completed = 0;

        // 逐个处理图片
        for (let i = 0; i < imageIds.length; i++) {
          const imageId = imageIds[i];

          try {
            await get().compressImage(imageId, level, customSize, customUnit);
          } catch (error) {
            console.error(`批量压缩中图片 ${imageId} 处理失败:`, error);
          }

          completed++;
          if (onProgress) {
            await onProgress(completed, total, imageId);
          }

          // 添加小延迟避免浏览器阻塞
          if (i < imageIds.length - 1) {
            await new Promise(resolve => setTimeout(resolve, 50));
          }
        }
      },

      // 批量操作辅助函数：在单个历史记录项中执行多个更新
      performBatchUpdate: (
        updates: Array<{ imageId: string; updates: Partial<ImageState> }>
      ) => {
        set(state => {
          updates.forEach(({ imageId, updates: imageUpdates }) => {
            const image = state.images.get(imageId);
            if (image) {
              Object.assign(image, imageUpdates);
            }
          });
        });

        // 批量更新后触发自动保存（使用防抖）
        const currentState = get();
        if (currentState.autoSaveEnabled && currentState.isStorageInitialized) {
          updates.forEach(({ imageId }) => {
            const updatedImage = currentState.images.get(imageId);
            if (!updatedImage) return;

            // 清除之前的定时器
            const existingTimer = currentState.saveTimers.get(imageId);
            if (existingTimer) {
              clearTimeout(existingTimer);
            }

            // 设置新的定时器
            const timer = setTimeout(() => {
              currentState
                .saveImageToPersistentStorage(imageId)
                .catch(error => {
                  console.error('批量更新自动保存图片失败:', error);
                });
              // 清除定时器引用
              set(state => {
                state.saveTimers.delete(imageId);
              });
            }, 2000); // 2秒后保存，避免频繁保存

            // 保存定时器引用
            set(state => {
              state.saveTimers.set(imageId, timer);
            });
          });
        }
      },
    })),
    {
      partialize: state => ({ images: state.images }),
      // 只对 images 状态进行历史记录，selectedImageIds 的变化不会创建历史记录
      // 使用深度比较来避免每次 Map 引用变化都创建历史记录
      equality: (pastState, currentState) => {
        // 如果 Map 大小不同，肯定是不同的状态
        if (pastState.images.size !== currentState.images.size) {
          return false;
        }

        // 比较每个图片的关键属性
        for (const [id, currentImage] of currentState.images) {
          const pastImage = pastState.images.get(id);
          if (!pastImage) {
            return false; // 新增了图片
          }

          // 比较关键属性，这些属性的变化才应该创建历史记录
          if (
            pastImage.status !== currentImage.status ||
            pastImage.processedUrl !== currentImage.processedUrl ||
            pastImage.backgroundColor !== currentImage.backgroundColor ||
            pastImage.backgroundImageUrl !== currentImage.backgroundImageUrl ||
            pastImage.backgroundImageId !== currentImage.backgroundImageId ||
            pastImage.resizedUrl !== currentImage.resizedUrl ||
            pastImage.convertedUrl !== currentImage.convertedUrl ||
            pastImage.compressedUrl !== currentImage.compressedUrl ||
            pastImage.name !== currentImage.name ||
            pastImage.width !== currentImage.width ||
            pastImage.height !== currentImage.height ||
            pastImage.eraseOperationCount !==
              currentImage.eraseOperationCount ||
            pastImage.currentEraseCanvasData !==
              currentImage.currentEraseCanvasData
          ) {
            return false; // 有关键属性发生了变化
          }
        }

        return true; // 所有关键属性都相同
      },
    }
  )
);

// 从 temporal store 中获取方法
export const undo = () => useImageStore.temporal.getState().undo();
export const redo = () => useImageStore.temporal.getState().redo();
export const clearHistory = () => useImageStore.temporal.getState().clear();

// 便捷的存储管理函数
export const initializeImageStorage = async () => {
  try {
    await useImageStore.getState().initializeStorage();
    return true;
  } catch (error) {
    console.error('存储初始化失败:', error);
    return false;
  }
};

export const loadPersistedImages = async () => {
  try {
    await useImageStore.getState().loadImagesFromPersistentStorage();
    return true;
  } catch (error) {
    console.error('加载持久化图片失败:', error);
    return false;
  }
};

export const saveAllImages = async () => {
  try {
    await useImageStore.getState().saveAllImagesToPersistentStorage();
    return true;
  } catch (error) {
    console.error('保存所有图片失败:', error);
    return false;
  }
};
