'use client';

import { usePathname, useRouter } from 'next/navigation';

interface NavigationMenuItemProps {
  title: string;
  path: string;
}

export function NavigationMenuItem({ title, path }: NavigationMenuItemProps) {
  const router = useRouter();
  const pathname = usePathname();

  // 如果当前路径匹配，则不显示该菜单项
  if (pathname.includes(path)) {
    return null;
  }

  return (
    <div
      className='flex items-center justify-between rounded-lg p-2.5 hover:bg-[#FFF0B4] cursor-pointer'
      onClick={() => router.push(path)}
    >
      <span className='text-base font-medium text-text-primary'>{title}</span>
    </div>
  );
}
