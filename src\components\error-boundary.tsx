'use client';

import { But<PERSON> } from '@/components/ui/Button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { AlertTriangle, RefreshCw } from 'lucide-react';
import { Component, ReactNode } from 'react';
import { useTranslations } from 'next-intl';

/**
 * ErrorBoundary 组件的 Props。
 */
interface Props {
  children: ReactNode;
}

/**
 * ErrorBoundary 组件的 State。
 */
interface State {
  hasError: boolean;
  error?: Error;
}

/**
 * Error UI component that uses translations
 */
function ErrorUI({
  error,
  onRefresh,
}: {
  error?: Error;
  onRefresh: () => void;
}) {
  const t = useTranslations();

  return (
    <div className='min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4'>
      <Card className='max-w-md w-full'>
        <CardHeader className='text-center'>
          <div className='w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4'>
            <AlertTriangle className='w-8 h-8 text-red-600' />
          </div>
          <CardTitle className='text-xl text-gray-900'>
            {t('error.title')}
          </CardTitle>
        </CardHeader>
        <CardContent className='text-center space-y-4'>
          <p className='text-gray-600'>{t('error.message')}</p>
          {error && (
            <details className='text-left'>
              <summary className='cursor-pointer text-sm text-gray-500 hover:text-gray-700'>
                {t('error.viewDetails')}
              </summary>
              <pre className='mt-2 text-xs bg-gray-100 p-2 rounded overflow-auto'>
                {error.message}
              </pre>
            </details>
          )}
          <Button onClick={onRefresh} className='w-full'>
            <RefreshCw className='w-4 h-4 me-2' />
            {t('error.refresh')}
          </Button>
        </CardContent>
      </Card>
    </div>
  );
}

/**
 * 一个 React 错误边界组件，用于捕获其子组件树中的 JavaScript 错误，
 * 并渲染一个备用的 UI，而不是让整个应用崩溃。
 */
export class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): State {
    // 更新 state，以便下一次渲染可以显示备用 UI。
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    // 你也可以在这里将错误日志上报给服务器
    // console.error('错误边界捕获到错误:', error, errorInfo);
    if (process.env.NODE_ENV === 'development') {
      console.error('错误边界捕获到错误:', error, errorInfo);
    }
  }

  render() {
    if (this.state.hasError) {
      // 渲染任何自定义的备用 UI
      return (
        <ErrorUI
          error={this.state.error}
          onRefresh={() => window.location.reload()}
        />
      );
    }

    return this.props.children;
  }
}
