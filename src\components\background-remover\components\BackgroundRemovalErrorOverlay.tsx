'use client';

import { Button } from '@/components/ui/Button';
import Image from 'next/image';
import { useTranslations } from 'next-intl';

interface BackgroundRemovalErrorOverlayProps {
  onTryEraser: () => void;
  onDeleteImage: () => void;
  isVisible: boolean;
}

/**
 * 背景去除失败的错误遮罩组件
 */
export function BackgroundRemovalErrorOverlay({
  onTryEraser,
  onDeleteImage,
  isVisible,
}: BackgroundRemovalErrorOverlayProps) {
  const singleImage = useTranslations('messages.singleImage');
  const common = useTranslations('common');

  if (!isVisible) return null;

  return (
    <div className='absolute inset-0 bg-gray-500/70 flex items-center justify-center z-30 rounded-lg'>
      <div className='rounded-lg p-6 max-w-sm mx-4 text-center'>
        {/* 错误图标 */}
        <div className='flex justify-center mb-4'>
          <Image
            src='/apps/icons/dialogWarning.svg'
            alt='error'
            width={64}
            height={64}
          />
        </div>

        {/* 错误文字 */}
        <div className='mb-6'>
          <p className='text-base text-white'>
            {singleImage('sorryCouldntRemove')}
          </p>
        </div>

        {/* 操作按钮 */}
        <div className='flex gap-4'>
          <Button
            onClick={onTryEraser}
            className='bg-transparent text-white border border-border-divider hover:bg-transparent'
          >
            <div className='flex items-center justify-center gap-2'>
              <span>{singleImage('tryBrush')}</span>
            </div>
          </Button>

          <Button
            onClick={onDeleteImage}
            className='min-w-30 text-text-primary hover:bg-brand-primary-hover'
          >
            <div className='flex items-center justify-center gap-2'>
              <span>{common('delete')}</span>
            </div>
          </Button>
        </div>
      </div>
    </div>
  );
}
