import Image from 'next/image';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/Button';
import { Dialog, DialogContent, DialogTitle } from '@/components/ui/Dialog';
import { VisuallyHidden } from '@/components/ui/VisuallyHidden';
import type { ImageState } from '@/store/imageStore';
import UploadIcon from '../../icons/Upload';
import BatchIcon from '../../icons/Batch';
import DeleteIcon from '../../icons/Delete';
import { useTranslations } from 'next-intl';
import { MAX_SINGLE_IMAGES_LIMIT } from '@/config/constants';
import { useTips } from '@/components/ui/Tips';

interface ImageHistoryBarProps {
  images: ImageState[];
  selectedImageIds: Set<string>;
  isLoadingApi: boolean;
  currentImageId: string | null;
  open: () => void;
  handleSelectImage: (imageId: string) => Promise<ImageState | undefined>;
  showDeleteConfirmation: (imageId: string) => void;
  deleteDialogOpen: boolean;
  setDeleteDialogOpen: (open: boolean) => void;
  confirmDeleteImage: () => void;
  cancelDeleteImage: () => void;
  imagesCount: number;
}

export const ImageHistoryBar = ({
  images,
  selectedImageIds,
  isLoadingApi,
  currentImageId,
  open,
  handleSelectImage,
  showDeleteConfirmation,
  deleteDialogOpen,
  setDeleteDialogOpen,
  confirmDeleteImage,
  cancelDeleteImage,
  imagesCount,
}: ImageHistoryBarProps) => {
  const router = useRouter();
  const common = useTranslations('common');
  const messages = useTranslations('messages');
  const { showTips } = useTips();

  const handleBatchEditorClick = () => {
    if (!isLoadingApi) {
      router.push('/batch-editor');
    }
  };

  return (
    <>
      {/* 底部图片历史记录栏 */}
      <div className='ps-6 pt-2 pb-6 flex-shrink-0'>
        <div className='max-w-7xl mx-auto'>
          <div className='flex items-center gap-2'>
            {/* 上传按钮 */}
            <div
              onClick={
                !isLoadingApi
                  ? () => {
                      // 检查图片数量限制
                      if (imagesCount >= 10) {
                        showTips(
                          'error',
                          messages('singleImage.imagesExceedLimit', {
                            count: MAX_SINGLE_IMAGES_LIMIT,
                          }),
                          4000
                        );
                        return;
                      }

                      open();
                    }
                  : undefined
              }
              className={`relative 
                w-20 h-20 border rounded-lg transition-all flex flex-col items-center justify-center group interactive-container
                hover:border-2 hover:text-brand-primary hover:border-primary
                ${
                  isLoadingApi
                    ? 'bg-gray-100 border-gray-300 cursor-not-allowed opacity-70'
                    : 'border-gray-300 hover:border-gray-400 hover:bg-gray-50 cursor-pointer'
                }
              `}
            >
              <>
                <UploadIcon className='icon-interactive' />
                <span className='text-[12px] text-center leading-[1.5] mt-0.5'>
                  {common('uploadImage')}
                </span>
              </>
            </div>

            {/* 历史图片列表 */}
            {images.map(image => {
              const isCurrentlyProcessingThisImage =
                isLoadingApi && image.id === currentImageId;
              const isClickable = !isLoadingApi;
              const isSelected = selectedImageIds.has(image.id);

              return (
                <div
                  key={image.id}
                  className={`relative group rounded-lg transition-all w-20 h-20 p-0.5
                    ${
                      isCurrentlyProcessingThisImage
                        ? 'border-4 border-background border-solid'
                        : isSelected
                          ? 'border-2 border-primary border-solid'
                          : 'border border-border border-solid'
                    }
                    ${
                      isClickable
                        ? 'cursor-pointer hover:border-[#FFE343]'
                        : 'cursor-default'
                    }
                    ${
                      isLoadingApi && !isCurrentlyProcessingThisImage
                        ? 'opacity-50 pointer-events-none'
                        : ''
                    }
                  `}
                  onClick={
                    isClickable ? () => handleSelectImage(image.id) : undefined
                  }
                >
                  <Image
                    src={image.previewUrl}
                    alt={image.name}
                    className='w-full h-full object-cover rounded-md'
                    width={60}
                    height={60}
                  />
                  {/* 当前处理中的图片遮罩 */}
                  {isCurrentlyProcessingThisImage && (
                    <div
                      className='absolute inset-0 flex items-center justify-center'
                      style={{
                        opacity: 'var(--opacity-3, 0.6)',
                        background: 'var(--color-01, #121212)',
                      }}
                    >
                      <Image
                        src='/apps/icons/loading.png'
                        alt='loading'
                        width={24}
                        height={24}
                        className='animate-spin'
                      />
                    </div>
                  )}

                  {/* 背景去除失败遮罩蒙层 */}
                  {image.status === 'bg-remove-failed' &&
                    !isCurrentlyProcessingThisImage && (
                      <div
                        className='absolute inset-0 rounded-md'
                        style={{
                          opacity: 'var(--opacity-3, 0.6)',
                          background: 'var(--color-01, #121212)',
                        }}
                      />
                    )}

                  {/* 背景去除失败错误图标 */}
                  {image.status === 'bg-remove-failed' &&
                    !isCurrentlyProcessingThisImage && (
                      <div className='absolute inset-0 flex items-center justify-center'>
                        <Image
                          src='/apps/icons/warning.svg'
                          alt='warning'
                          width={24}
                          height={24}
                          className='relative z-10'
                        />
                      </div>
                    )}
                  {/* 删除按钮 */}
                  <Button
                    variant='ghost'
                    size='icon'
                    className='interactive-container absolute top-1 end-1 w-[22px] h-[22px] rounded-sm p-1 bg-[#686868] hover:bg-[#686868] opacity-0 group-hover:opacity-100 transition-opacity duration-200 cursor-pointer'
                    onClick={e => {
                      e.stopPropagation();
                      showDeleteConfirmation(image.id);
                    }}
                  >
                    <DeleteIcon className='icon-interactive-white' />
                  </Button>
                </div>
              );
            })}
            {images?.length >= 3 && (
              <div
                onClick={handleBatchEditorClick}
                className={`relative
                w-20 h-20 border rounded-lg transition-all flex flex-col items-center justify-center group interactive-container
                hover:border-2 hover:text-brand-primary hover:border-primary
                ${
                  isLoadingApi
                    ? 'bg-gray-100 border-gray-300 cursor-not-allowed opacity-70'
                    : 'border-gray-300 hover:border-gray-400 hover:bg-gray-50 cursor-pointer'
                }
              `}
              >
                <>
                  <BatchIcon className='icon-interactive' />
                  <span className='text-[12px] text-center leading-[1.5] mt-0.5'>
                    Batch
                    <br />
                    Editor
                  </span>
                </>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* 删除确认弹窗 */}
      <Dialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <DialogContent className='w-[542px] bg-white rounded-2xl shadow-[0px_4px_8px_0px_rgba(19,19,20,0.5)] border-0 p-0'>
          <VisuallyHidden>
            <DialogTitle>{messages('singleImage.confirmDelete')}</DialogTitle>
          </VisuallyHidden>
          {/* 顶部关闭按钮区域 */}
          <div className='h-10 relative rounded-t-2xl w-full border-b border-[#e7e7e7]'></div>

          {/* 主要内容区域 */}
          <div className='px-8 pb-8'>
            <div className='w-full flex flex-col gap-6'>
              <div className='flex items-center justify-start gap-5'>
                {/* 问号图标 */}
                <div>
                  <Image
                    src='/apps/icons/dialogInfo.svg'
                    alt='dialogInfo'
                    width={64}
                    height={64}
                  />
                </div>
                <div>
                  {/* 标题 */}
                  <p className='text-[#121212] text-[18px] font-medium leading-[1.5] text-center'>
                    {messages('singleImage.confirmDelete')}
                  </p>
                </div>
              </div>
              {/* 按钮区域 */}
              <div className='flex gap-3 w-full justify-end'>
                <Button
                  variant='outline'
                  onClick={cancelDeleteImage}
                  className='border-[#e7e7e7] bg-white hover:bg-gray-50'
                >
                  {common('cancel')}
                </Button>
                <Button onClick={confirmDeleteImage}>{common('delete')}</Button>
              </div>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
};
