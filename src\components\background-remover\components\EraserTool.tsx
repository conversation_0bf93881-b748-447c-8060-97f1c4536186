'use client';

import { Label } from '@/components/ui/Label';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/Popover';
import { Slider } from '@/components/ui/Slider';
import { X } from 'lucide-react';
import { useCallback, useEffect, useState, useRef } from 'react';
import { useTranslations } from 'next-intl';
import { cn } from '@/lib/base';
import EraseIcon from '@/components/icons/erase';
import RestoreIcon from '@/components/icons/restore';

/**
 * 橡皮擦工具组件的 Props。
 */
interface EraserToolProps {
  isEraseMode: boolean;
  isRestoreMode: boolean;
  eraseBrushSize: number;
  onEraseModeChange: (isEraseMode: boolean, isRestoreMode: boolean) => void;
  onBrushSizeChange: (size: number) => void;
  children: React.ReactNode;
  // 外部控制弹窗关闭的prop
  forceClose?: boolean;
  // 外部控制弹窗打开的prop
  forceOpen?: boolean;
}

/**
 * 橡皮擦工具组件
 */
export function EraserTool({
  isEraseMode,
  isRestoreMode,
  eraseBrushSize,
  onEraseModeChange,
  onBrushSizeChange,
  children,
  forceClose = false,
  forceOpen = false,
}: EraserToolProps) {
  const eraseRestore = useTranslations('singleImage.eraseRestore');
  const [isPopoverOpen, setIsPopoverOpen] = useState(false);
  const [tempBrushSize, setTempBrushSize] = useState<number | null>(null);
  const brushSizeTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // 默认橡皮擦大小
  const DEFAULT_BRUSH_SIZE = 80;

  const handleClosePopover = useCallback(() => {
    setIsPopoverOpen(false);
    setTempBrushSize(null);
    // 清除防抖定时器
    if (brushSizeTimeoutRef.current) {
      clearTimeout(brushSizeTimeoutRef.current);
      brushSizeTimeoutRef.current = null;
    }
    // 关闭弹窗时重置所有状态
    onEraseModeChange(false, false);
    onBrushSizeChange(DEFAULT_BRUSH_SIZE);
  }, [onEraseModeChange, onBrushSizeChange, DEFAULT_BRUSH_SIZE]);

  // 处理外部强制关闭
  useEffect(() => {
    if (forceClose && isPopoverOpen) {
      handleClosePopover();
    }
  }, [forceClose, isPopoverOpen, handleClosePopover]);

  // 处理外部强制打开
  useEffect(() => {
    if (forceOpen && !isPopoverOpen) {
      setIsPopoverOpen(true);
      // 打开弹窗时自动激活橡皮擦模式
      onEraseModeChange(true, false);
    }
  }, [forceOpen, isPopoverOpen, onEraseModeChange]);

  // 组件卸载时清除定时器
  useEffect(() => {
    return () => {
      if (brushSizeTimeoutRef.current) {
        clearTimeout(brushSizeTimeoutRef.current);
      }
    };
  }, []);

  const handleEraseMode = () => {
    onEraseModeChange(true, false);
  };

  const handleRestoreMode = () => {
    onEraseModeChange(false, true);
  };

  const handleBrushSizeChange = (value: number[]) => {
    setTempBrushSize(value[0]);

    // 清除之前的定时器
    if (brushSizeTimeoutRef.current) {
      clearTimeout(brushSizeTimeoutRef.current);
    }

    // 使用防抖，避免频繁更新
    brushSizeTimeoutRef.current = setTimeout(() => {
      if (eraseBrushSize !== value[0]) {
        onBrushSizeChange(value[0]);
      }
      setTempBrushSize(null);
    }, 150); // 150ms防抖延迟
  };

  const handleBrushSizeCommit = (value: number[]) => {
    // 立即清除防抖定时器并执行更新
    if (brushSizeTimeoutRef.current) {
      clearTimeout(brushSizeTimeoutRef.current);
      brushSizeTimeoutRef.current = null;
    }

    setTempBrushSize(null);
    if (eraseBrushSize !== value[0]) {
      onBrushSizeChange(value[0]);
    }
  };

  const handleOpenPopover = () => {
    setIsPopoverOpen(true);
    // 打开弹窗时自动激活橡皮擦模式
    onEraseModeChange(true, false);
  };

  // 提取条件变量提高可读性
  const isEraseActive = isEraseMode && !isRestoreMode;

  return (
    <Popover
      open={isPopoverOpen}
      onOpenChange={open => {
        // 只有当显式关闭时才处理，禁用点击外部自动关闭
        if (!open && isPopoverOpen) {
          setTempBrushSize(null);
        }
      }}
    >
      <PopoverTrigger asChild>
        <div onClick={handleOpenPopover}>{children}</div>
      </PopoverTrigger>
      <PopoverContent
        className='w-86 h-107 p-0 rounded-2xl border border-[#E7E7E7] shadow-[0px_10px_32px_0px_rgba(220,223,228,0.08),0px_8px_16px_0px_rgba(220,223,228,0.12),0px_7px_14px_0px_rgba(220,223,228,0.16)] flex flex-col'
        onPointerDownOutside={e => e.preventDefault()}
        onInteractOutside={e => e.preventDefault()}
        sideOffset={-170}
      >
        {/* 固定头部 - 不参与滚动 */}
        <div className='flex-shrink-0 p-4 pb-0'>
          <div className='flex justify-between items-center mb-4'>
            <Label className='text-base font-bold'>
              {eraseRestore('title')}
            </Label>
            {/* 右上角关闭按钮 */}
            <button
              onClick={handleClosePopover}
              className='w-6 h-6 flex items-center justify-center rounded-full hover:bg-gray-100 transition-colors cursor-pointer'
            >
              <X className='w-6 h-6 text-text-primary' strokeWidth={1.5} />
            </button>
          </div>
          <div className='h-px bg-[#E7E7E7] -mx-4' />
        </div>

        {/* 可滚动内容区域 */}
        <div className='flex-1 overflow-y-auto p-4 pt-4'>
          <div className='space-y-4'>
            {/* 模式选择 */}
            <div className='flex gap-4'>
              {/* Erase Mode */}
              <button
                onClick={handleEraseMode}
                className={cn(
                  'h-24 w-[150px] rounded-lg border-2 flex flex-col items-center justify-center gap-1 transition-colors cursor-pointer',
                  isEraseActive
                    ? 'border-primary bg-yellow-50'
                    : 'border-[#e7e7e7] bg-white hover:bg-gray-50'
                )}
              >
                <div className='w-5 h-5 relative'>
                  <EraseIcon
                    className={cn(
                      'w-full h-full icon-interactive',
                      isEraseActive && 'active'
                    )}
                  />
                </div>
                <span
                  className={cn(
                    'text-sm font-medium',
                    isEraseActive ? 'text-[#ffcc03]' : 'text-[#121212]'
                  )}
                >
                  {eraseRestore('erase')}
                </span>
              </button>

              {/* Restore Mode */}
              <button
                onClick={handleRestoreMode}
                className={cn(
                  'h-24 w-[150px] rounded-lg border-2 flex flex-col items-center justify-center gap-1 transition-colors cursor-pointer',
                  isRestoreMode
                    ? 'border-primary bg-yellow-50'
                    : 'border-[#e7e7e7] bg-white hover:bg-gray-50'
                )}
              >
                <div className='w-5 h-5 relative'>
                  <RestoreIcon
                    className={cn(
                      'w-full h-full icon-interactive',
                      isRestoreMode && 'active'
                    )}
                  />
                </div>
                <span
                  className={cn(
                    'text-sm font-medium',
                    isRestoreMode ? 'text-[#ffcc03]' : 'text-[#121212]'
                  )}
                >
                  {eraseRestore('restore')}
                </span>
              </button>
            </div>

            {/* 画笔大小 */}
            <div className='space-y-3'>
              <div className='flex justify-between items-center mb-1'>
                <Label
                  htmlFor='erase-brush-size'
                  className='text-sm font-medium'
                >
                  {eraseRestore('brushSize')}
                </Label>
                <span className='text-sm text-muted-foreground'>
                  {tempBrushSize ?? eraseBrushSize}px
                </span>
              </div>
              <Slider
                id='erase-brush-size'
                min={16}
                max={320}
                step={1}
                value={[tempBrushSize ?? eraseBrushSize]}
                onValueChange={handleBrushSizeChange}
                onValueCommit={handleBrushSizeCommit}
                className='w-full'
              />
            </div>
          </div>
        </div>
      </PopoverContent>
    </Popover>
  );
}
