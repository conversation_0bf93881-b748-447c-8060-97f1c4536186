# 超级简单购买弹窗使用指南

## 🎯 设计理念

你说得对！作为使用者，我们根本不需要关心组件内部如何实现，只需要：
- 一行代码调用弹窗
- 弹窗自己处理所有状态（包括关闭按钮）
- 完全不需要Hook或状态管理

## 🚀 快速开始

### 1. 应用根部设置（只需一次）

在你的应用根部（如 `app/layout.tsx` 或 `_app.tsx`）添加容器组件：

```tsx
import { PurchaseModalContainer } from '@/components/common';

export default function RootLayout({ children }) {
  return (
    <html>
      <body>
        {children}
        {/* 添加这一行，全局只需要一次 */}
        <PurchaseModalContainer />
      </body>
    </html>
  );
}
```

### 2. 任何地方使用（一行代码）

```tsx
import { showPurchaseModal } from '@/components/common';

function MyComponent() {
  const handleUpgrade = () => {
    // 就这一行代码！
    showPurchaseModal();
  };

  return <button onClick={handleUpgrade}>升级</button>;
}
```

## 📖 API 参考

### showPurchaseModal(options?)

显示购买弹窗的全局方法。

```tsx
showPurchaseModal({
  defaultPlan?: 'pro' | 'pro-plus',        // 默认选中的计划
  defaultBilling?: 'monthly' | 'yearly',   // 默认计费周期
  onPurchase?: (plan) => Promise<void>,     // 购买回调
});
```

### 返回值

返回一个 Promise，可以用来处理购买结果：

```tsx
const result = await showPurchaseModal({
  onPurchase: async (plan) => {
    // 购买逻辑
  },
});

if (result) {
  console.log('购买成功:', result);
} else {
  console.log('用户取消了购买');
}
```

## 🎨 使用示例

### 1. 最简单的使用

```tsx
import { showPurchaseModal } from '@/components/common';

const handleUpgrade = () => {
  showPurchaseModal(); // 就这一行！
};
```

### 2. 带参数的使用

```tsx
const handleUpgrade = () => {
  showPurchaseModal({
    defaultPlan: 'pro-plus',
    defaultBilling: 'yearly',
    onPurchase: async (plan) => {
      // 调用你的购买API
      await fetch('/api/purchase', {
        method: 'POST',
        body: JSON.stringify(plan),
      });
      alert('购买成功！');
    },
  });
};
```

### 3. Promise方式处理结果

```tsx
const handleUpgrade = async () => {
  try {
    const result = await showPurchaseModal({
      defaultPlan: 'pro',
      onPurchase: async (plan) => {
        // 购买逻辑
        return plan;
      },
    });
    
    if (result) {
      console.log('购买完成:', result);
    }
  } catch (error) {
    console.error('购买失败:', error);
  }
};
```

## 🧩 快速组件

### QuickPurchaseButton

封装了 `showPurchaseModal` 的按钮组件：

```tsx
import { QuickPurchaseButton } from '@/components/common';

function Header() {
  return (
    <QuickPurchaseButton 
      plan="pro-plus"
      billing="yearly"
      onSuccess={(plan) => console.log('购买成功:', plan)}
    >
      升级到Pro+
    </QuickPurchaseButton>
  );
}
```

### FeatureLockPrompt

功能限制提示组件：

```tsx
import { FeatureLockPrompt } from '@/components/common';

function AdvancedFeature() {
  const isProUser = false; // 从用户状态获取

  if (!isProUser) {
    return (
      <FeatureLockPrompt
        featureName="AI背景去除"
        requiredPlan="pro"
      />
    );
  }

  return <YourAdvancedFeature />;
}
```

## 🎪 实际使用场景

### 导航栏升级按钮

```tsx
function Header() {
  return (
    <header>
      <div>Logo</div>
      <QuickPurchaseButton size="sm">升级</QuickPurchaseButton>
    </header>
  );
}
```

### 功能限制提示

```tsx
function AITool() {
  const userPlan = 'free'; // 从用户状态获取

  if (userPlan === 'free') {
    return (
      <FeatureLockPrompt 
        featureName="AI工具" 
        requiredPlan="pro-plus"
      />
    );
  }

  return <AIToolInterface />;
}
```

### 试用期提醒

```tsx
function TrialReminder({ daysLeft }) {
  return (
    <div className="bg-orange-50 p-4 rounded">
      <p>试用期剩余 {daysLeft} 天</p>
      <QuickPurchaseButton size="sm">
        立即续费
      </QuickPurchaseButton>
    </div>
  );
}
```

### 任何按钮都可以触发

```tsx
function MyComponent() {
  return (
    <div>
      {/* 普通按钮 */}
      <button onClick={() => showPurchaseModal()}>
        升级账户
      </button>

      {/* 链接 */}
      <a href="#" onClick={(e) => {
        e.preventDefault();
        showPurchaseModal();
      }}>
        解锁高级功能
      </a>

      {/* 卡片点击 */}
      <div 
        className="cursor-pointer"
        onClick={() => showPurchaseModal({ defaultPlan: 'pro-plus' })}
      >
        点击升级
      </div>
    </div>
  );
}
```

## ✨ 优势对比

### 传统Hook方式 ❌
```tsx
// 需要管理状态
const [isOpen, setIsOpen] = useState(false);
const [selectedPlan, setSelectedPlan] = useState('pro');
// ... 更多状态

// 需要传递很多props
<PurchaseModal 
  isOpen={isOpen}
  onClose={() => setIsOpen(false)}
  selectedPlan={selectedPlan}
  onSelectPlan={setSelectedPlan}
  // ... 更多props
/>
```

### 新的简单方式 ✅
```tsx
// 一行代码搞定
showPurchaseModal();
```

## 🔧 高级用法

### 集成真实购买API

```tsx
const handlePurchase = () => {
  showPurchaseModal({
    onPurchase: async (plan) => {
      // 1. 创建订单
      const order = await fetch('/api/orders', {
        method: 'POST',
        body: JSON.stringify({
          planId: plan.id,
          billing: plan.billing,
        }),
      }).then(res => res.json());

      // 2. 跳转到支付页面
      window.location.href = order.paymentUrl;
    },
  });
};
```

### 条件性显示

```tsx
const handleFeatureClick = () => {
  const userPlan = getUserPlan(); // 获取用户计划
  
  if (userPlan === 'free') {
    // 免费用户显示升级弹窗
    showPurchaseModal({ defaultPlan: 'pro' });
  } else {
    // 付费用户直接使用功能
    useAdvancedFeature();
  }
};
```

## 🎯 总结

这种方式的核心优势：

1. **零学习成本** - 一行代码 `showPurchaseModal()` 搞定
2. **无状态管理** - 不需要任何Hook或状态
3. **全局可用** - 在任何组件中都能调用
4. **自动处理** - 弹窗的打开、关闭、加载状态都自动处理
5. **完全封装** - 使用者完全不需要关心内部实现

就像你说的，我们只需要在适当的时机调用一个方法，弹窗就出现了，关闭按钮也是内部自己处理的。这才是真正好用的组件设计！
