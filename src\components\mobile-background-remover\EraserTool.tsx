'use client';

import { Label } from '@/components/ui/Label';
import {
  Drawer,
  DrawerContent,
  DrawerHeader,
  DrawerTitle,
  DrawerTrigger,
} from '@/components/ui/Drawer';
import { Slider } from '@/components/ui/Slider';
import { X } from 'lucide-react';
import { useCallback, useEffect, useState } from 'react';
import { useTranslations } from 'next-intl';
import { cn } from '@/lib/base';
import EraseIcon from '@/components/icons/erase';
import RestoreIcon from '@/components/icons/restore';

/**
 * 橡皮擦工具组件的 Props。
 */
interface EraserToolProps {
  isEraseMode: boolean;
  isRestoreMode: boolean;
  eraseBrushSize: number;
  onEraseModeChange: (isEraseMode: boolean, isRestoreMode: boolean) => void;
  onBrushSizeChange: (size: number) => void;
  children: React.ReactNode;
  // 外部控制弹窗关闭的prop
  forceClose?: boolean;
}

/**
 * 橡皮擦工具组件
 */
export function MbileEraserTool({
  isEraseMode,
  isRestoreMode,
  eraseBrushSize,
  onEraseModeChange,
  onBrushSizeChange,
  children,
  forceClose = false,
}: EraserToolProps) {
  const [isPopoverOpen, setIsPopoverOpen] = useState(false);
  const [tempBrushSize, setTempBrushSize] = useState<number | null>(null);
  const eraseRestore = useTranslations('singleImage.eraseRestore');

  // 默认橡皮擦大小
  const DEFAULT_BRUSH_SIZE = 80;

  const handleClosePopover = useCallback(() => {
    setIsPopoverOpen(false);
    setTempBrushSize(null);
    // 关闭弹窗时重置所有状态
    onEraseModeChange(false, false);
    onBrushSizeChange(DEFAULT_BRUSH_SIZE);
  }, [onEraseModeChange, onBrushSizeChange, DEFAULT_BRUSH_SIZE]);

  // 处理外部强制关闭
  useEffect(() => {
    if (forceClose && isPopoverOpen) {
      handleClosePopover();
    }
  }, [forceClose, isPopoverOpen, handleClosePopover]);

  const handleEraseMode = () => {
    onEraseModeChange(true, false);
  };

  const handleRestoreMode = () => {
    onEraseModeChange(false, true);
  };

  const handleBrushSizeChange = (value: number[]) => {
    setTempBrushSize(value[0]);
  };

  const handleBrushSizeCommit = (value: number[]) => {
    setTempBrushSize(null);
    if (eraseBrushSize !== value[0]) {
      onBrushSizeChange(value[0]);
    }
  };

  const handleOpenPopover = () => {
    setIsPopoverOpen(true);
    // 打开弹窗时自动激活橡皮擦模式
    onEraseModeChange(true, false);
  };

  // 提取条件变量提高可读性
  const isEraseActive = isEraseMode && !isRestoreMode;

  return (
    <Drawer
      open={isPopoverOpen}
      onOpenChange={open => {
        // 只有当显式关闭时才处理，禁用点击外部自动关闭
        if (!open && isPopoverOpen) {
          setTempBrushSize(null);
        }
      }}
      modal={false}
    >
      <DrawerTrigger asChild>
        <div onClick={handleOpenPopover}>{children}</div>
      </DrawerTrigger>
      <DrawerContent
        className='p-6 rounded-t-2xl border border-[#E7E7E7] shadow-[0px_10px_32px_0px_rgba(220,223,228,0.08),0px_8px_16px_0px_rgba(220,223,228,0.12),0px_7px_14px_0px_rgba(220,223,228,0.16)] data-[vaul-drawer-direction=bottom]:mt-0'
        onPointerDownOutside={e => e.preventDefault()}
        onInteractOutside={e => e.preventDefault()}
      >
        <DrawerHeader className='flex justify-between mb-4 p-0 -mt-1'>
          <DrawerTitle className='text-[16px] font-bold text-[#121212] text-start'>
            {eraseRestore('title')}
          </DrawerTitle>
          {/* 右上角关闭按钮 */}
          <button
            onClick={handleClosePopover}
            className='absolute top-5 end-5 w-6 h-6 flex items-center justify-center rounded-full hover:bg-gray-100 transition-colors cursor-pointer'
          >
            <X className='w-6 h-6 text-[#121212]' strokeWidth={1.5} />
          </button>
        </DrawerHeader>

        {/* 模式选择 */}
        <div className='space-y-4'>
          <div className='flex gap-4 pt-4 border-t border-[#E7E7E7]'>
            {/* Erase Mode */}
            <button
              onClick={handleEraseMode}
              className={cn(
                'flex-1 h-24 rounded-lg border-2 flex flex-col items-center justify-center gap-1 transition-colors cursor-pointer',
                isEraseActive
                  ? 'border-[#ffcc03] bg-yellow-50'
                  : 'border-[#e7e7e7] bg-white hover:bg-gray-50'
              )}
            >
              <div className='w-5 h-5 relative'>
                <EraseIcon
                  className={cn(
                    'w-full h-full icon-interactive',
                    isEraseActive && 'active'
                  )}
                />
              </div>
              <span
                className={cn(
                  'text-sm font-medium',
                  isEraseActive ? 'text-[#ffcc03]' : 'text-[#121212]'
                )}
              >
                {eraseRestore('erase')}
              </span>
            </button>

            {/* Restore Mode */}
            <button
              onClick={handleRestoreMode}
              className={cn(
                'flex-1 h-24 rounded-lg border-2 flex flex-col items-center justify-center gap-1 transition-colors cursor-pointer',
                isRestoreMode
                  ? 'border-[#ffcc03] bg-yellow-50'
                  : 'border-[#e7e7e7] bg-white hover:bg-gray-50'
              )}
            >
              <div className='w-5 h-5 relative'>
                <RestoreIcon
                  className={cn(
                    'w-full h-full icon-interactive',
                    isRestoreMode && 'active'
                  )}
                />
              </div>
              <span
                className={cn(
                  'text-sm font-medium',
                  isRestoreMode ? 'text-[#ffcc03]' : 'text-[#121212]'
                )}
              >
                {eraseRestore('restore')}
              </span>
            </button>
          </div>
        </div>

        {/* 画笔大小 */}
        <div className='space-y-3 pt-4'>
          <div className='flex justify-between items-center mb-1'>
            <Label
              htmlFor='erase-brush-size'
              className='text-sm font-medium text-[#878787] mb-2'
            >
              {eraseRestore('brushSize')}
            </Label>
            <span className='text-sm text-muted-foreground'>
              {tempBrushSize ?? eraseBrushSize}px
            </span>
          </div>
          <Slider
            id='erase-brush-size'
            min={16}
            max={320}
            step={1}
            value={[tempBrushSize ?? eraseBrushSize]}
            onValueChange={handleBrushSizeChange}
            onValueCommit={handleBrushSizeCommit}
            className='w-full'
          />
        </div>
      </DrawerContent>
    </Drawer>
  );
}
