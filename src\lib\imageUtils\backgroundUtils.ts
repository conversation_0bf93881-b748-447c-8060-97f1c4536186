/**
 * 背景相关的工具函数
 * 处理背景图片分类生成、背景颜色验证等功能
 */

import { BACKGROUND_CATEGORIES_CONFIG } from '@/config/backgroundCategories';

/**
 * 预设图片接口
 */
export interface PresetImage {
  id: string;
  name: string;
  url: string;
}

/**
 * 预设图片分类接口
 */
export interface PresetCategory {
  id: string;
  i18nKey: string;
  images: PresetImage[];
}

/**
 * 动态生成预设图片分类
 * @returns 预设分类数组，包含国际化键
 */
export const generatePresetCategories = (): PresetCategory[] => {
  return Object.entries(BACKGROUND_CATEGORIES_CONFIG).map(
    ([categoryId, config]) => ({
      id: categoryId,
      i18nKey: config.i18nKey,
      images: Array.from({ length: config.imageCount }, (_, index) => ({
        id: `${categoryId}${index + 1}`,
        name: `${categoryId}${index + 1}`,
        url: `/apps/images/background/${categoryId}/${index + 1}.png`,
      })),
    })
  );
};
