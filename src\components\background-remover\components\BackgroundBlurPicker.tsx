'use client';

import { Label } from '@/components/ui/Label';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/Popover';
import { Slider } from '@/components/ui/Slider';
import { Switch } from '@/components/ui/Switch';
import { X } from 'lucide-react';
import { useState } from 'react';
import { useTranslations } from 'next-intl';

/**
 * 背景模糊选择器组件的 Props。
 */
interface BackgroundBlurPickerProps {
  isBlurEnabled: boolean;
  blurAmount: number;
  onBlurSettingsChange: (settings: {
    isBlurEnabled: boolean;
    blurAmount: number;
  }) => void;
  children: React.ReactNode;
}

/**
 * 背景模糊选择器组件
 */
export function BackgroundBlurPicker({
  isBlurEnabled,
  blurAmount,
  onBlurSettingsChange,
  children,
}: BackgroundBlurPickerProps) {
  const [open, setOpen] = useState(false);
  // 拖动滑块时临时的模糊值
  const [tempBlurAmount, setTempBlurAmount] = useState<number | null>(null);
  const singleImage = useTranslations('singleImage');

  const handleClosePopover = () => {
    setOpen(false);
    setTempBlurAmount(null);
  };

  const handleSwitchChange = (checked: boolean) => {
    const newBlurAmount = checked && blurAmount === 0 ? 5 : blurAmount;
    onBlurSettingsChange({
      isBlurEnabled: checked,
      blurAmount: newBlurAmount,
    });
  };

  // 节流处理滑块变化，避免拖动时卡顿
  const handleSliderChange = (value: number[]) => {
    setTempBlurAmount(value[0]);
  };

  const handleSliderCommit = (value: number[]) => {
    setTempBlurAmount(null);
    if (blurAmount !== value[0]) {
      onBlurSettingsChange({
        isBlurEnabled,
        blurAmount: value[0],
      });
    }
  };

  return (
    <Popover
      open={open}
      onOpenChange={newOpen => {
        setOpen(newOpen);
        if (!newOpen) {
          setTempBlurAmount(null);
        }
      }}
    >
      <PopoverTrigger asChild>{children}</PopoverTrigger>
      <PopoverContent
        className='w-86 h-107 p-4 rounded-2xl border border-[#E7E7E7] shadow-[0px_10px_32px_0px_rgba(220,223,228,0.08),0px_8px_16px_0px_rgba(220,223,228,0.12),0px_7px_14px_0px_rgba(220,223,228,0.16)]'
        sideOffset={-230}
      >
        <div className='flex justify-between items-center mb-4'>
          <h3 className='text-base font-bold text-text-primary'>
            {singleImage('interface.blurBackground')}
          </h3>
          {/* 右上角关闭按钮 */}
          <button
            onClick={handleClosePopover}
            className='w-6 h-6 flex items-center justify-center rounded-full hover:bg-gray-100 transition-colors cursor-pointer'
          >
            <X className='w-6 h-6 text-text-primary' strokeWidth={1.5} />
          </button>
        </div>

        <div className='h-px bg-[#E7E7E7] -mx-4 mb-4' />

        <div className='space-y-4'>
          {/* 启用模糊开关 */}
          <div className='flex items-center justify-between'>
            <Label
              htmlFor='blur-switch'
              className='text-base font-semibold text-text-primary'
            >
              {singleImage('backgroundBlur.enableBackgroundBlur')}
            </Label>
            <Switch
              id='blur-switch'
              checked={isBlurEnabled}
              onCheckedChange={handleSwitchChange}
            />
          </div>

          {/* 模糊程度控制 */}
          {isBlurEnabled && (
            <div className='space-y-3 pt-2'>
              <div className='flex justify-between items-center mb-1'>
                <Label
                  htmlFor='blur-amount'
                  className='text-sm font-medium text-[#878787]'
                >
                  {singleImage('backgroundBlur.blurAmount')}
                </Label>
                <span className='text-sm text-[#878787]'>
                  {tempBlurAmount ?? blurAmount}px
                </span>
              </div>
              <Slider
                id='blur-amount'
                min={0}
                max={20}
                step={1}
                value={[tempBlurAmount ?? blurAmount]}
                disabled={!isBlurEnabled}
                onValueChange={handleSliderChange}
                onValueCommit={handleSliderCommit}
                className='w-full'
              />
            </div>
          )}
        </div>
      </PopoverContent>
    </Popover>
  );
}
