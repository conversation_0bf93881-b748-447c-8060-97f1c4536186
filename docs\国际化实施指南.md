# Next.js 国际化实施指南

## 概述

本项目已成功实施了基于 `next-intl` 的国际化方案，支持15种语言：英文（en）、简体中文（zh）、繁体中文（zh-TW）、日语（ja）、法语（fr）、西班牙语（es）、意大利语（it）、阿拉伯语（ar）、德语（de）、韩语（ko）、葡萄牙语（pt）、俄语（ru）、泰语（th）、土耳其语（tr）、印尼语（id）。采用 **无路由国际化** 方案，URL 保持不变，通过 Cookie 和语言切换器管理语言状态。

## 技术架构

### 核心技术栈
- **Next.js 15.3.2** - 使用 App Router
- **next-intl** - 国际化核心库（无路由模式）
- **TypeScript** - 类型安全的翻译支持
- **Cookie** - 语言偏好持久化

### 路由结构
```
/                       # 主页（根据 Cookie 显示对应语言）
/remove-background      # 背景移除页面
/batch-editor          # 批量编辑器
/account-page          # 账户页面
/login                 # 登录页面
```

**特点：**
- URL 不包含语言前缀
- 语言通过 Cookie 持久化
- 页面内容根据用户选择的语言动态切换

## 文件结构

```
src/
├── i18n/
│   └── request.ts            # next-intl 请求配置
├── messages/
│   ├── en.json              # 英文翻译文件
│   ├── zh.json              # 简体中文翻译文件
│   ├── zh-TW.json           # 繁体中文翻译文件
│   ├── ja.json              # 日语翻译文件
│   ├── fr.json              # 法语翻译文件
│   ├── es.json              # 西班牙语翻译文件
│   ├── it.json              # 意大利语翻译文件
│   ├── ar.json              # 阿拉伯语翻译文件
│   ├── de.json              # 德语翻译文件
│   ├── ko.json              # 韩语翻译文件
│   ├── pt.json              # 葡萄牙语翻译文件
│   ├── ru.json              # 俄语翻译文件
│   ├── th.json              # 泰语翻译文件
│   ├── tr.json              # 土耳其语翻译文件
│   └── id.json              # 印尼语翻译文件
├── components/
│   └── i18n/
│       └── LanguageSwitcher.tsx    # 语言切换组件
└── app/
    ├── layout.tsx           # 根布局（集成 NextIntlClientProvider）
    ├── page.tsx             # 主页
    ├── remove-background/   # 背景移除页面
    ├── batch-editor/        # 批量编辑器
    ├── account-page/        # 账户页面
    └── login/               # 登录页面
```

## 配置说明

### 1. 国际化请求配置 (`src/i18n/request.ts`)

```typescript
import { getRequestConfig } from 'next-intl/server';
import { getUserLocale } from '@/services/locale';

export default getRequestConfig(async () => {
  // 从服务中获取用户选择的语言
  const locale = await getUserLocale();

  return {
    locale,
    messages: (await import(`../messages/${locale}.json`)).default,
  };
});
```

这个配置文件会在每次请求时执行，读取用户的语言偏好并加载对应的翻译文件。

### 2. Next.js 配置 (`next.config.ts`)

```typescript
import createNextIntlPlugin from 'next-intl/plugin';

const withNextIntl = createNextIntlPlugin('./src/i18n/request.ts');
export default withNextIntl(nextConfig);
```

### 3. 根布局配置 (`src/app/layout.tsx`)

```typescript
import { NextIntlClientProvider } from 'next-intl';
import { getLocale } from 'next-intl/server';

export default async function RootLayout({ children }) {
  const locale = await getLocale();

  return (
    <html lang={locale}>
      <body>
        <NextIntlClientProvider>
          {children}
        </NextIntlClientProvider>
      </body>
    </html>
  );
}
```

## 使用指南

### 1. 在组件中使用翻译

#### 客户端组件
```typescript
'use client';
import { useTranslations } from 'next-intl';

export function RemoveBackgroundComponent() {
  // 获取不同命名空间的翻译
  const common = useTranslations('common');
  const tools = useTranslations('tools');
  const singleImage = useTranslations('singleImage');
  const messages = useTranslations('messages');

  return (
    <div>
      <h1>{tools('removeBackground')}</h1>
      <p>{singleImage('initial.uploadImageToRemove')}</p>
      <button>{common('upload')}</button>
      <div>{singleImage('initial.dragAndDrop')}</div>
      <span>{singleImage('initial.supportedFormats')}</span>

      {/* 背景选项 */}
      <div>
        <button>{singleImage('interface.changeBackgroundColors')}</button>
        <button>{singleImage('interface.changeBackgroundPhotos')}</button>
      </div>

      {/* 错误提示 */}
      <div className="error">
        {messages('singleImage.unableToOpenImage')}
      </div>
    </div>
  );
}

export function BatchEditorComponent() {
  const batchEditor = useTranslations('batchEditor');
  const common = useTranslations('common');

  return (
    <div>
      <h1>{batchEditor('interface.background')}</h1>
      <div>
        <button>{batchEditor('interface.resize')}</button>
        <button>{batchEditor('interface.rename')}</button>
        <button>{batchEditor('interface.compress')}</button>
      </div>

      {/* 平台选择 */}
      <div>
        <h3>{batchEditor('resize.marketplacePlatforms')}</h3>
        <button>{batchEditor('resize.amazon')}</button>
        <button>{batchEditor('resize.tiktokShop')}</button>
      </div>

      <button>{common('apply')}</button>
    </div>
  );
}
```

#### 服务端组件
```typescript
import { getTranslations } from 'next-intl/server';

export default async function ServerRemoveBackgroundPage() {
  const tools = await getTranslations('tools');
  const singleImage = await getTranslations('singleImage');
  const common = await getTranslations('common');

  return (
    <div>
      <h1>{tools('removeBackground')}</h1>
      <p>{singleImage('initial.uploadImageToRemove')}</p>
      <div>
        <span>{singleImage('initial.supportedFormats')}</span>
        <p>{singleImage('initial.noImageTryThese')}</p>
      </div>
      <button>{common('upload')}</button>
    </div>
  );
}

export default async function ServerBatchEditorPage() {
  const tools = await getTranslations('tools');
  const batchEditor = await getTranslations('batchEditor');
  const account = await getTranslations('account');

  return (
    <div>
      <h1>{tools('batchEditor')}</h1>
      <div>
        <h2>{batchEditor('resize.marketplacePlatforms')}</h2>
        <ul>
          <li>{batchEditor('resize.amazon')}</li>
          <li>{batchEditor('resize.tiktokShop')}</li>
          <li>{batchEditor('resize.shopee')}</li>
        </ul>
      </div>
      <div>
        <h2>{batchEditor('resize.socialMediaPlatforms')}</h2>
        <ul>
          <li>{batchEditor('resize.instagramStory')}</li>
          <li>{batchEditor('resize.facebookPost')}</li>
          <li>{batchEditor('resize.tiktokPost')}</li>
        </ul>
      </div>
      <p>{account('usageChart')}</p>
    </div>
  );
}
```

### 2. 语言配置

首先创建语言配置文件：

```typescript
// src/i18n/config.ts
export type Locale = 'en' | 'zh' | 'zh-TW' | 'ja' | 'fr' | 'es' | 'it' | 'ar' | 'de' | 'ko' | 'pt' | 'ru' | 'th' | 'tr' | 'id';

export const defaultLocale: Locale = 'zh';

export const locales: readonly Locale[] = ['en', 'zh', 'zh-TW', 'ja', 'fr', 'es', 'it', 'ar', 'de', 'ko', 'pt', 'ru', 'th', 'tr', 'id'] as const;

export const localeConfig = {
  en: {
    name: 'English',
    flag: '🇺🇸',
    dir: 'ltr',
  },
  zh: {
    name: '简体中文',
    flag: '🇨🇳',
    dir: 'ltr',
  },
  'zh-TW': {
    name: '繁體中文',
    flag: '🇹🇼',
    dir: 'ltr',
  },
  ja: {
    name: '日本語',
    flag: '🇯🇵',
    dir: 'ltr',
  },
  fr: {
    name: 'Français',
    flag: '🇫🇷',
    dir: 'ltr',
  },
  es: {
    name: 'Español',
    flag: '🇪🇸',
    dir: 'ltr',
  },
  it: {
    name: 'Italiano',
    flag: '🇮🇹',
    dir: 'ltr',
  },
  ar: {
    name: 'العربية',
    flag: '🇸🇦',
    dir: 'rtl',
  },
  de: {
    name: 'Deutsch',
    flag: '🇩🇪',
    dir: 'ltr',
  },
  ko: {
    name: '한국어',
    flag: '🇰🇷',
    dir: 'ltr',
  },
  pt: {
    name: 'Português',
    flag: '🇵🇹',
    dir: 'ltr',
  },
  ru: {
    name: 'Русский',
    flag: '🇷🇺',
    dir: 'ltr',
  },
  th: {
    name: 'ไทย',
    flag: '🇹🇭',
    dir: 'ltr',
  },
  tr: {
    name: 'Türkçe',
    flag: '🇹🇷',
    dir: 'ltr',
  },
  id: {
    name: 'Bahasa Indonesia',
    flag: '🇮🇩',
    dir: 'ltr',
  },
} as const;
```

### 3. 语言服务

创建服务端语言管理函数：

```typescript
// src/services/locale.ts
'use server';

import { cookies } from 'next/headers';

export type Locale = 'en' | 'zh';

export const defaultLocale: Locale = 'zh';

const COOKIE_NAME = 'RN_LOCALE';

export async function getUserLocale(): Promise<Locale> {
  const cookieStore = await cookies();
  return (cookieStore.get(COOKIE_NAME)?.value as Locale) || defaultLocale;
}

export async function setUserLocale(locale: Locale) {
  const cookieStore = await cookies();
  cookieStore.set(COOKIE_NAME, locale, {
    path: '/',
    maxAge: 60 * 60 * 24 * 365, // 1 year
  });
}
```

### 4. 语言切换组件

使用优雅的 Server Action 方式：

```typescript
// src/components/i18n/LanguageSwitcher.tsx
'use client';

import { useLocale } from 'next-intl';
import { useState, useTransition } from 'react';
import { ChevronDown, Check } from 'lucide-react';
import { Locale, localeConfig } from '@/i18n/config';
import { setUserLocale } from '@/services/locale';

export function LanguageSwitcher() {
  const locale = useLocale() as Locale;
  const [isPending, startTransition] = useTransition();
  const [isOpen, setIsOpen] = useState(false);

  const handleLanguageChange = (newLocale: Locale) => {
    if (newLocale === locale) return;

    startTransition(() => {
      // 使用 Server Action 设置语言 - 无需手动刷新页面！
      setUserLocale(newLocale);
    });

    setIsOpen(false);
  };

  return (
    <div className='relative'>
      <button
        onClick={() => setIsOpen(!isOpen)}
        disabled={isPending}
        className='flex items-center gap-2 px-3 py-2 text-sm border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50'
      >
        <span>{localeConfig[locale].flag}</span>
        <span>{localeConfig[locale].name}</span>
        <ChevronDown className={`w-4 h-4 transition-transform ${isOpen ? 'rotate-180' : ''}`} />
      </button>

      {isOpen && (
        <div className='absolute top-full left-0 mt-1 w-full bg-white border border-gray-300 rounded-md shadow-lg z-50'>
          {locales.map(loc => (
            <button
              key={loc.code}
              onClick={() => handleLanguageChange(loc.code)}
              disabled={isPending}
              className={`w-full flex items-center justify-between px-3 py-2 text-sm text-left hover:bg-gray-50 disabled:opacity-50 transition-colors ${
                loc.code === locale ? 'bg-blue-50 text-blue-600' : 'text-gray-700'
              }`}
            >
              <div className='flex items-center gap-2'>
                <span>{loc.flag}</span>
                <span>{loc.name}</span>
              </div>
              {loc.code === locale && (
                <Check className='h-4 w-4 text-blue-600' />
              )}
            </button>
          ))}
        </div>
      )}
    </div>
  );
}
```

### 5. 使用语言切换器

```typescript
import { LanguageSwitcher } from '@/components/i18n/LanguageSwitcher';

// 在头部组件中使用
<LanguageSwitcher />
```

## 语言切换工作原理

### Server Action 机制

新的语言切换方式使用 Next.js 的 Server Actions，比传统的客户端 Cookie + 页面刷新更优雅：

#### 工作流程：

1. **用户点击语言切换**
   ```typescript
   const handleLanguageChange = (newLocale: Locale) => {
     startTransition(() => {
       setUserLocale(newLocale); // Server Action
     });
   };
   ```

2. **Server Action 执行**
   ```typescript
   // src/services/locale.ts
   'use server';
   export async function setUserLocale(locale: Locale) {
     const cookieStore = await cookies();
     cookieStore.set(COOKIE_NAME, locale, { /* options */ });
   }
   ```

3. **自动重新渲染**
   - Server Action 完成后，Next.js 自动触发页面重新渲染
   - 重新执行 `getRequestConfig`
   - `getUserLocale()` 读取新的 Cookie 值
   - 加载对应语言的翻译文件

4. **组件更新**
   - `useLocale()` 返回新的语言值
   - 所有 `useTranslations()` 组件自动更新

#### 优势对比：

**旧方式（粗暴）：**
```typescript
// 客户端设置 Cookie
document.cookie = `locale=${newLocale}; path=/`;
// 强制刷新整个页面
router.refresh();
```

**新方式（优雅）：**
```typescript
// 服务端设置 Cookie + 自动重新渲染
setUserLocale(newLocale);
```

#### 关键优势：

1. **无需手动刷新**：Server Action 自动触发重新渲染
2. **状态保持**：React 状态不会丢失
3. **更好的用户体验**：没有明显的页面闪烁
4. **类型安全**：服务端函数提供更好的类型检查

## 翻译文件管理

### 文件结构
- `src/messages/en.json` - 英文翻译
- `src/messages/zh.json` - 简体中文翻译
- `src/messages/zh-TW.json` - 繁体中文翻译
- `src/messages/ja.json` - 日语翻译
- `src/messages/fr.json` - 法语翻译
- `src/messages/es.json` - 西班牙语翻译
- `src/messages/it.json` - 意大利语翻译
- `src/messages/ar.json` - 阿拉伯语翻译
- `src/messages/de.json` - 德语翻译
- `src/messages/ko.json` - 韩语翻译
- `src/messages/pt.json` - 葡萄牙语翻译
- `src/messages/ru.json` - 俄语翻译
- `src/messages/th.json` - 泰语翻译
- `src/messages/tr.json` - 土耳其语翻译
- `src/messages/id.json` - 印尼语翻译

### 翻译键命名规范

项目按照产品功能模块组织翻译键：

```json
{
  "common": {
    "upload": "Upload",
    "download": "Download",
    "cancel": "Cancel",
    "confirm": "Confirm",
    "apply": "Apply",
    "processing": "Processing"
  },
  "tools": {
    "removeBackground": "Remove BG",
    "batchEditor": "Batch Editor"
  },
  "auth": {
    "signIn": "Sign In",
    "signUp": "Sign Up",
    "myAccount": "My account"
  },
  "singleImage": {
    "initial": {
      "uploadImageToRemove": "Upload Image to Remove Background",
      "dragAndDrop": "Drag and drop your image here",
      "uploadImage": "Upload Image"
    },
    "interface": {
      "changeBackgroundColors": "Change Background Colors",
      "changeBackgroundPhotos": "Change Background Photos",
      "eraseRestore": "Erase / Restore"
    },
    "backgroundColors": {
      "customColor": "Custom Color",
      "presetColor": "Preset Color"
    }
  },
  "batchEditor": {
    "interface": {
      "background": "Background",
      "resize": "Resize",
      "rename": "Rename"
    },
    "resize": {
      "marketplacePlatforms": "For Marketplace Platforms",
      "socialMediaPlatforms": "For Social Media Platforms",
      "tiktokShop": "TikTok shop",
      "amazon": "Amazon"
    }
  },
  "messages": {
    "singleImage": {
      "unableToOpenImage": "Unable to open image.The URL is invalid or wrong.",
      "imagesExceedLimit": "The number of images exceeds the limit, and a maximum of 10 images can be uploaded."
    },
    "batchEditor": {
      "allProcessedSuccessfully": "All processed successfully",
      "sizeTooSmall": "Size too small. Minimum value is 100 px."
    }
  },
  "account": {
    "transactionHistory": "Transaction History",
    "usageChart": "Usage Chart",
    "receivedFreeCredits": "You've received 5 free AI credits today."
  },
  "mobile": {
    "selectPhotos": "Please select 1–10 photos",
    "allowAccessPhotos": "Allow Access to Your Photos",
    "bgColors": "BG  Colors"
  }
}
```

### 类型安全
项目配置了完整的TypeScript支持，翻译键会有自动补全和类型检查。

## SEO 优化

### 1. 元数据国际化
每个语言版本都有独立的元数据：

```typescript
export async function generateMetadata({
  params: { locale },
}: {
  params: { locale: string };
}): Promise<Metadata> {
  const messages = await getMessages({ locale });
  
  return {
    title: messages.meta?.defaultTitle,
    description: messages.meta?.defaultDescription,
    keywords: messages.meta?.keywords,
  };
}
```

### 2. 语言标签
HTML 标签会自动设置正确的 `lang` 属性。

### 3. 搜索引擎优化
- 每种语言都有独立的URL
- 支持 hreflang 标签
- 可以生成多语言 sitemap

## 性能优化

### 1. 代码分割
- 翻译文件按需加载
- 只加载当前语言的翻译

### 2. 静态生成
- 支持静态生成多语言页面
- 预渲染所有语言版本

### 3. 缓存策略
- 翻译文件可以被浏览器缓存
- 支持 CDN 缓存

## 部署注意事项

### 1. 环境变量
确保生产环境中的语言配置正确。

### 2. 服务器配置
如果使用反向代理，确保正确处理语言路径。

### 3. CDN 配置
配置 CDN 时注意语言路径的缓存策略。

## 扩展新语言

### 1. 添加语言配置
```typescript
// src/i18n/config.ts
export const locales = ['en', 'zh', 'es'] as const; // 添加西班牙语

export const localeConfig = {
  // ... 现有配置
  es: {
    name: 'Español',
    flag: '🇪🇸',
    dir: 'ltr',
  },
} as const;
```

### 2. 创建翻译文件
创建 `src/messages/es.json` 并添加所有翻译键。

### 3. 更新类型定义
TypeScript 会自动识别新的语言类型。

## 常见问题

### Q: 如何处理复数形式？
A: next-intl 支持 ICU 消息格式，可以处理复数：
```json
{
  "items": "{count, plural, =0 {no items} =1 {one item} other {# items}}"
}
```

### Q: 如何处理日期和数字格式化？
A: 使用 next-intl 的格式化功能：
```typescript
const t = useTranslations();
const formatDateTime = useFormatter();

const formattedDate = formatDateTime(new Date(), {
  year: 'numeric',
  month: 'long',
  day: 'numeric'
});
```

### Q: 如何处理动态内容？
A: 使用变量替换：
```json
{
  "welcome": "Welcome, {name}!"
}
```

```typescript
t('welcome', { name: 'John' })
```

## 最佳实践

### 1. 语言切换优化

**推荐使用 Server Actions：**
```typescript
// ✅ 推荐：使用 Server Action
startTransition(() => {
  setUserLocale(newLocale);
});

// ❌ 不推荐：客户端 Cookie + 强制刷新
document.cookie = `locale=${newLocale}; path=/`;
router.refresh();
```

**优势：**
- 无页面闪烁
- 保持 React 状态
- 更好的用户体验
- 类型安全

### 2. 翻译键组织

**按功能模块分组：**
```json
{
  "singleImage": {
    "initial": { "uploadImageToRemove": "..." },
    "interface": { "changeBackgroundColors": "..." }
  },
  "batchEditor": {
    "interface": { "background": "..." },
    "background": { "color": "..." }
  }
}
```

### 3. 组件使用模式

**在组件中使用翻译：**
```typescript
export function MyComponent() {
  const common = useTranslations('common');
  const singleImage = useTranslations('singleImage');

  return (
    <div>
      <h1>{singleImage('initial.uploadImageToRemove')}</h1>
      <button>{common('upload')}</button>
    </div>
  );
}
```

### 4. 性能优化

- 翻译文件按需加载
- 使用 `startTransition` 避免阻塞 UI
- 合理组织翻译键避免重复

## 总结

本国际化方案提供了：
- ✅ 完整的多语言支持
- ✅ 优雅的语言切换机制
- ✅ 类型安全的翻译
- ✅ 无 URL 路径的语言切换
- ✅ 优秀的用户体验
- ✅ 易于维护和扩展

项目现在已经完全支持国际化，使用了 Next.js 15 + next-intl 的最佳实践，可以轻松添加新语言和管理翻译内容。
