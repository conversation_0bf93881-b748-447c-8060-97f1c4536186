'use client';

import React from 'react';
import { PurchaseModalContainer, showPurchaseModal, PurchaseButton, FeatureLock } from '@/components/common';

/**
 * 购买弹窗测试页面
 */
export default function PurchaseTestPage() {
  return (
    <div className="min-h-screen bg-gray-50">
      {/* 添加弹窗容器 */}
      <PurchaseModalContainer />
      
      <div className="max-w-2xl mx-auto py-8 px-4 space-y-8">
        <div className="text-center">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">
            购买弹窗测试
          </h1>
          <p className="text-gray-600">
            测试超级简单的购买弹窗功能
          </p>
        </div>

        {/* 基础使用 */}
        <section className="bg-white rounded-lg shadow-sm p-6">
          <h2 className="text-xl font-semibold mb-4">基础使用</h2>
          <div className="space-y-4">
            <div>
              <button 
                onClick={() => showPurchaseModal()}
                className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded"
              >
                显示购买弹窗（默认设置）
              </button>
            </div>
            
            <div>
              <button 
                onClick={() => showPurchaseModal({
                  defaultPlan: 'pro',
                  defaultBilling: 'monthly',
                  onPurchase: async (plan) => {
                    alert(`购买成功: ${plan.name} - ${plan.price}`);
                  }
                })}
                className="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded"
              >
                显示购买弹窗（Pro月付）
              </button>
            </div>
          </div>
        </section>

        {/* 快速组件 */}
        <section className="bg-white rounded-lg shadow-sm p-6">
          <h2 className="text-xl font-semibold mb-4">快速组件</h2>
          <div className="space-y-4">
            <div>
              <h3 className="font-medium mb-2">购买按钮</h3>
              <div className="flex gap-4">
                <PurchaseButton>
                  默认升级
                </PurchaseButton>
                <PurchaseButton 
                  plan="pro" 
                  billing="monthly"
                  className="bg-blue-500 hover:bg-blue-600"
                >
                  Pro月付
                </PurchaseButton>
              </div>
            </div>
            
            <div>
              <h3 className="font-medium mb-2">功能锁定提示</h3>
              <FeatureLock 
                featureName="AI背景去除" 
                requiredPlan="pro"
              />
            </div>
          </div>
        </section>

        {/* 使用说明 */}
        <section className="bg-blue-50 border border-blue-200 rounded-lg p-6">
          <h2 className="text-xl font-semibold text-blue-900 mb-4">
            使用说明
          </h2>
          <div className="text-sm text-blue-800 space-y-2">
            <p><strong>1. 应用根部设置：</strong></p>
            <pre className="bg-blue-100 p-2 rounded text-xs overflow-x-auto">
{`// 在 app/layout.tsx 中添加
<PurchaseModalContainer />`}
            </pre>
            
            <p><strong>2. 任何地方调用：</strong></p>
            <pre className="bg-blue-100 p-2 rounded text-xs overflow-x-auto">
{`import { showPurchaseModal } from '@/components/common';

// 一行代码显示弹窗
showPurchaseModal();`}
            </pre>
          </div>
        </section>
      </div>
    </div>
  );
}
