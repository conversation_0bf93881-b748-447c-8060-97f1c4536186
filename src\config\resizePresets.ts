// 预设类型定义
export interface FixedPreset {
  id: string;
  name: string;
  i18nKey?: string;
  width: number;
  height: number;
  icon: string;
  mode: 'fixed';
}

export interface RatioPreset {
  id: string;
  name: string;
  i18nKey?: string;
  aspectRatio: number;
  icon: string;
  mode: 'ratio';
}

export type ResizePreset = FixedPreset | RatioPreset;

export interface ResizePresetCategory {
  title: string;
  titleI18nKey?: string;
  presets: ResizePreset[];
}

export type ResizePresetCategories = {
  marketplace: ResizePresetCategory;
  social: ResizePresetCategory;
  ratio: ResizePresetCategory;
  custom: ResizePresetCategory;
  original: ResizePresetCategory;
};

// 平台尺寸预设数据
export const RESIZE_PRESETS: ResizePresetCategories = {
  marketplace: {
    title: 'For Marketplace Platforms',
    titleI18nKey: 'resize.marketplacePlatforms',
    presets: [
      {
        id: 'tiktok',
        name: 'TikTok',
        i18nKey: 'resize.tiktokShop',
        width: 1600,
        height: 1600,
        icon: '/apps/images/platform/tiktok.png',
        mode: 'fixed',
      },
      {
        id: 'amazon',
        name: 'Amazon',
        i18nKey: 'resize.amazon',
        width: 2000,
        height: 2000,
        icon: '/apps/images/platform/amazon.png',
        mode: 'fixed',
      },
      {
        id: 'ebay',
        name: 'eBay',
        i18nKey: 'resize.ebay',
        width: 1600,
        height: 1600,
        icon: '/apps/images/platform/ebay.png',
        mode: 'fixed',
      },
      {
        id: 'postmark',
        name: 'Postmark',
        i18nKey: 'resize.postmark',
        width: 1080,
        height: 1080,
        icon: '/apps/images/platform/postmark.png',
        mode: 'fixed',
      },
      {
        id: 'depop',
        name: 'Depop',
        i18nKey: 'resize.depop',
        width: 1280,
        height: 1280,
        icon: '/apps/images/platform/depop.png',
        mode: 'fixed',
      },
      {
        id: 'mercari',
        name: 'Mercari',
        i18nKey: 'resize.mercari',
        width: 1080,
        height: 1080,
        icon: '/apps/images/platform/mercari.png',
        mode: 'fixed',
      },
      {
        id: 'mercado-libre',
        name: 'Mercado Libre',
        i18nKey: 'resize.mercadoLibre',
        width: 1080,
        height: 1080,
        icon: '/apps/images/platform/mercadoLibre.png',
        mode: 'fixed',
      },
      {
        id: 'shopee',
        name: 'Shopee',
        i18nKey: 'resize.shopee',
        width: 1080,
        height: 1080,
        icon: '/apps/images/platform/shopee.png',
        mode: 'fixed',
      },
      {
        id: 'shopify-square',
        name: 'Shopify square',
        i18nKey: 'resize.shopifySquare',
        width: 2048,
        height: 2048,
        icon: '/apps/images/platform/shopifyPortrait.png',
        mode: 'fixed',
      },
      {
        id: 'shopify-landscape',
        name: 'Shopify landscape',
        i18nKey: 'resize.shopifyLandscape',
        width: 2000,
        height: 1800,
        icon: '/apps/images/platform/shopifyPortrait.png',
        mode: 'fixed',
      },
      {
        id: 'shopify-portrait',
        name: 'Shopify portrait',
        i18nKey: 'resize.shopifyPortrait',
        width: 1600,
        height: 2000,
        icon: '/apps/images/platform/shopifyPortrait.png',
        mode: 'fixed',
      },
      {
        id: 'lazada',
        name: 'Lazada',
        i18nKey: 'resize.lazada',
        width: 1080,
        height: 1080,
        icon: '/apps/images/platform/lazada.png',
        mode: 'fixed',
      },
      {
        id: 'etsy',
        name: 'Etsy',
        i18nKey: 'resize.etsy',
        width: 2700,
        height: 2025,
        icon: '/apps/images/platform/etsy.png',
        mode: 'fixed',
      },
      {
        id: 'vinted',
        name: 'Vinted',
        i18nKey: 'resize.vinted',
        width: 800,
        height: 600,
        icon: '/apps/images/platform/vinted.png',
        mode: 'fixed',
      },
    ],
  },
  social: {
    title: 'For Social Media Platforms',
    titleI18nKey: 'resize.socialMediaPlatforms',
    presets: [
      {
        id: 'ins-story',
        name: 'Instagram story',
        i18nKey: 'resize.instagramStory',
        width: 1080,
        height: 1920,
        icon: '/apps/images/platform/ins.png',
        mode: 'fixed',
      },
      {
        id: 'ins-post',
        name: 'Instagram post',
        i18nKey: 'resize.instagramPost',
        width: 1080,
        height: 1080,
        icon: '/apps/images/platform/ins.png',
        mode: 'fixed',
      },
      {
        id: 'fb-cover',
        name: 'Facebook cover',
        i18nKey: 'resize.facebookCover',
        width: 820,
        height: 312,
        icon: '/apps/images/platform/facebook.png',
        mode: 'fixed',
      },
      {
        id: 'fb-post',
        name: 'Facebook post',
        i18nKey: 'resize.facebookPost',
        width: 1200,
        height: 628,
        icon: '/apps/images/platform/facebook.png',
        mode: 'fixed',
      },
      {
        id: 'fb-marketplace',
        name: 'Facebook Marketplace',
        i18nKey: 'resize.facebookMarketplace',
        width: 1200,
        height: 628,
        icon: '/apps/images/platform/facebook.png',
        mode: 'fixed',
      },
      {
        id: 'tiktok-post',
        name: 'TikTok post',
        i18nKey: 'resize.tiktokPost',
        width: 1080,
        height: 1080,
        icon: '/apps/images/platform/tiktok.png',
        mode: 'fixed',
      },
      {
        id: 'tiktok-cover',
        name: 'TikTok cover',
        i18nKey: 'resize.tiktokCover',
        width: 1080,
        height: 1440,
        icon: '/apps/images/platform/tiktok.png',
        mode: 'fixed',
      },
      {
        id: 'youtube-cover',
        name: 'YouTube cover',
        i18nKey: 'resize.youtubeCover',
        width: 1280,
        height: 1920,
        icon: '/apps/images/platform/youtube.png',
        mode: 'fixed',
      },
      {
        id: 'youtube-channel',
        name: 'YouTube channel',
        i18nKey: 'resize.youtubeChannel',
        width: 2560,
        height: 1440,
        icon: '/apps/images/platform/youtube.png',
        mode: 'fixed',
      },
      {
        id: 'twitter-cover',
        name: 'X cover',
        i18nKey: 'resize.twitterCover',
        width: 2560,
        height: 1440,
        icon: '/apps/images/platform/x.png',
        mode: 'fixed',
      },
      {
        id: 'twitter-post',
        name: 'X post',
        i18nKey: 'resize.twitterPost',
        width: 2560,
        height: 1440,
        icon: '/apps/images/platform/x.png',
        mode: 'fixed',
      },
    ],
  },
  ratio: {
    title: 'Ratio Size',
    titleI18nKey: 'resize.ratioSize',
    presets: [
      {
        id: 'square',
        name: 'Square(1:1)',
        i18nKey: 'resize.square',
        aspectRatio: 1, // 1:1
        icon: '/apps/images/scale/scale11.svg',
        mode: 'ratio',
      },
      {
        id: 'ratio-3-2',
        name: '3:2',
        aspectRatio: 3 / 2, // 1.5
        icon: '/apps/images/scale/scale32.svg',
        mode: 'ratio',
      },
      {
        id: 'ratio-4-3',
        name: '4:3',
        aspectRatio: 4 / 3, // 1.333...
        icon: '/apps/images/scale/scale43.svg',
        mode: 'ratio',
      },
      {
        id: 'ratio-5-3',
        name: '5:3',
        aspectRatio: 5 / 3, // 1.666...
        icon: '/apps/images/scale/scale53.svg',
        mode: 'ratio',
      },
      {
        id: 'ratio-5-4',
        name: '5:4',
        aspectRatio: 5 / 4, // 1.25
        icon: '/apps/images/scale/scale54.svg',
        mode: 'ratio',
      },
      {
        id: 'ratio-7-5',
        name: '7:5',
        aspectRatio: 7 / 5, // 1.4
        icon: '/apps/images/scale/scale75.svg',
        mode: 'ratio',
      },
      {
        id: 'ratio-16-9',
        name: '16:9',
        aspectRatio: 16 / 9, // 1.777...
        icon: '/apps/images/scale/scale169.svg',
        mode: 'ratio',
      },
      {
        id: 'passport-2x2',
        name: 'Passport & Photo(2 x 2in)',
        i18nKey: 'resize.passportId',
        width: 600,
        height: 600,
        icon: '/apps/images/scale/passport.svg',
        mode: 'fixed',
      },
      {
        id: 'passport-3.5x4.5',
        name: 'Passport & Photo(3.5 x 4.5in)',
        i18nKey: 'resize.passport35x45',
        width: 1050,
        height: 1350,
        icon: '/apps/images/scale/passport.svg',
        mode: 'fixed',
      },
    ],
  },
  custom: {
    title: 'Custom Size',
    titleI18nKey: 'resize.customSize',
    presets: [], // Custom Size 不需要预设列表
  },
  original: {
    title: 'Original Size',
    titleI18nKey: 'resize.originalSize',
    presets: [], // Original Size 不需要预设列表
  },
};
