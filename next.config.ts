import type { NextConfig } from "next";
import createNextIntlPlugin from 'next-intl/plugin';

const nextConfig: NextConfig = {
  // 多项目部署配置选项：

  // 选项1：独立部署 + 反向代理（推荐）
  // 当前配置：项目独立部署，通过 Nginx 代理到 /apps/remove-background
  // 优势：各项目完全独立，互不影响

  // 选项2：子路径部署
  // 如果需要直接部署到子路径，取消注释下面的配置：
  basePath: '/apps',
  assetPrefix: '/apps',

  // 图片配置：允许外部图片域名
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'lh3.googleusercontent.com',
        port: '',
        pathname: '/**',
      },
    ],
  },
};

const withNextIntl = createNextIntlPlugin();
export default withNextIntl(nextConfig);
