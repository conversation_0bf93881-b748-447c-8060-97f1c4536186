# 权益消耗接口集成文档

## 概述

本文档描述了 `/api/v1/permission/consume` 权益消耗接口的集成实现，该接口用于在用户成功使用去除背景功能后扣除相应的积分。

## 接口定义

### 请求参数

```typescript
interface ConsumePermissionRequest {
  consumed_num?: number; // 消耗数量，非必须，默认为1
  description?: string;  // 描述，非必须
}
```

### 响应参数

```typescript
interface ConsumePermissionResponse {
  code?: number;  // 响应码，非必须
  data?: null;    // 数据，非必须
  msg?: string;   // 消息，非必须
}
```

**响应说明**:
- `code`: 响应状态码，通常 `1` 表示成功，其他值表示失败
- `data`: 数据字段，权益消耗接口通常返回 `null`
- `msg`: 响应消息，包含成功或失败的描述信息

### 接口地址

- **URL**: `POST /api/v1/permission/consume`
- **Content-Type**: `application/json`

## 集成位置

权益消耗接口已集成到以下位置，在去除背景API成功后自动调用：

### 1. 单个图片处理
- **文件**: `src/hooks/background-remover/useImageUpload.ts`
- **函数**: `handleRemoveBackground`
- **描述**: `"Remove BG"`
- **消耗数量**: `1`

### 2. 批量图片处理
- **文件**: `src/hooks/batch-editor/useBatchImageUpload.ts`
- **函数**: 批量处理逻辑
- **描述**: `"Batch Editor RemoveBG"`
- **消耗数量**: `1` (每张图片)

### 3. 移动端图片处理
- **文件**: `src/components/mobile-background-remover/BackgroundRemover.tsx`
- **函数**: `handleRemoveBackground`
- **描述**: `"Remove BG"`
- **消耗数量**: `1`

## 调用时机

权益消耗接口在以下时机被调用：

1. **前置检查**: 在调用 `removeBackground` API 之前检查用户剩余积分
2. **成功条件**: `removeBackground` API 调用成功并返回处理后的图片
3. **调用位置**: 在图片处理成功后，创建 `objectURL` 之后
4. **积分更新**: 权益消耗成功后刷新用户信息以更新积分显示
5. **错误处理**: 权益消耗失败不会影响主要功能，只记录错误日志

## 积分检查机制

### 单个图片处理
- **检查时机**: 在开始处理前检查剩余积分是否 ≥ 1
- **不足处理**: 显示错误提示"积分不足！无法处理图片"并终止处理
- **积分更新**: 处理成功后自动刷新用户积分信息

### 批量图片处理
- **检查时机**: 在开始批量处理前检查剩余积分是否 ≥ 图片数量
- **不足处理**: 显示详细错误提示"积分不足！需要 X 积分，当前剩余 Y 积分"并终止处理
- **积分更新**: 批量处理完成后自动刷新用户积分信息

## 错误处理策略

```typescript
try {
  await consumePermission({
    consumed_num: 1,
    description: 'Remove BG'
  });
  console.log('权益消耗成功');
} catch (consumeError) {
  console.error('权益消耗失败:', consumeError);
  // 权益消耗失败不影响主要功能，只记录错误
}
```

### 错误处理原则

1. **非阻塞**: 权益消耗失败不会阻止图片处理功能
2. **日志记录**: 所有错误都会被记录到控制台
3. **用户体验**: 用户仍然可以看到处理后的图片，即使积分扣除失败

## 使用示例

### 基本用法

```typescript
import { consumePermission } from '@/api';

// 单个图片处理
await consumePermission({
  consumed_num: 1,
  description: 'Remove BG'
});

// 批量图片处理
await consumePermission({
  consumed_num: 1,
  description: 'Batch Editor RemoveBG'
});
```

### 在组件中使用

```typescript
const handleImageProcess = async () => {
  try {
    // 1. 检查积分是否足够
    const userInfo = useAuthStore.getState().userInfo;
    const remainingCredits = userInfo?.score ?? 0;

    if (remainingCredits < 1) {
      showTips('error', '积分不足！无法处理图片', 3000);
      return;
    }

    // 2. 处理图片
    const result = await removeBackground({ image: imageData });

    // 3. 消耗权益
    const consumeResult = await consumePermission({
      consumed_num: 1,
      description: 'Remove BG'
    });

    // 4. 检查权益消耗结果（可选）
    if (consumeResult.code === 1) {
      console.log('权益消耗成功:', consumeResult.msg);

      // 5. 刷新用户积分信息
      const authStore = useAuthStore.getState();
      await authStore.checkAuth();
    } else {
      console.warn('权益消耗失败:', consumeResult.msg);
    }

    // 6. 更新UI
    setProcessedImage(result);
  } catch (error) {
    console.error('处理失败:', error);
  }
};
```

### 批量处理示例

```typescript
const handleBatchProcess = async (imageIds: string[]) => {
  // 1. 检查积分是否足够
  const userInfo = useAuthStore.getState().userInfo;
  const remainingCredits = userInfo?.score ?? 0;
  const requiredCredits = imageIds.length;

  if (remainingCredits < requiredCredits) {
    showTips(
      'error',
      `积分不足！需要 ${requiredCredits} 积分，当前剩余 ${remainingCredits} 积分`,
      5000
    );
    return;
  }

  // 2. 执行批量处理
  try {
    await batchRemoveBackground(imageIds);
  } catch (error) {
    console.error('批量处理失败:', error);
  }
};
```

## 注意事项

1. **积分预检查**: 所有处理操作前都会检查用户剩余积分，不足时会阻止处理
2. **幂等性**: 每次成功的图片处理都会调用一次权益消耗接口
3. **并发处理**: 批量处理时，每张图片的权益消耗是独立的
4. **错误恢复**: 权益消耗失败不会影响图片处理结果
5. **积分同步**: 处理成功后会自动刷新用户积分信息
6. **用户体验**: 积分不足时会显示明确的错误提示
7. **日志监控**: 建议监控权益消耗的成功率和失败原因

## 后续优化建议

1. **批量优化**: 考虑批量处理时合并权益消耗请求
2. **重试机制**: 添加权益消耗失败的重试逻辑
3. **用户提示**: 在权益不足时给用户明确提示
4. **缓存机制**: 缓存用户剩余积分信息，减少API调用
