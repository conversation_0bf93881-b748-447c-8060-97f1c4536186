'use client';

import React, { useState } from 'react';
import { useTranslations } from 'next-intl';
import { Check, X, ChevronDown } from 'lucide-react';
import { Dialog, DialogContent } from '@/components/ui/Dialog';
import { Button } from '@/components/ui/Button';
import { cn } from '@/lib/base';

export interface PurchaseOptions {
  defaultPlan?: 'pro' | 'pro-plus';
  defaultBilling?: 'monthly' | 'yearly';
  onPurchase?: (plan: {
    id: 'pro' | 'pro-plus';
    name: string;
    price: string;
    billing: 'monthly' | 'yearly';
  }) => Promise<void> | void;
}

// Global modal state
const globalModalState = {
  isOpen: false,
  options: {} as PurchaseOptions,
  resolve: null as ((value: unknown) => void) | null,
};

// 全局弹窗组件实例
let globalSetModalState: React.Dispatch<
  React.SetStateAction<typeof globalModalState>
> | null = null;

/**
 * Show purchase modal global method
 */
export function showPurchaseModal(
  options: PurchaseOptions = {}
): Promise<unknown> {
  return new Promise(resolve => {
    if (globalSetModalState) {
      globalSetModalState({
        isOpen: true,
        options,
        resolve,
      });
    } else {
      console.warn(
        'PurchaseModal not mounted. Please add <PurchaseModalContainer /> to your app.'
      );
      resolve(null);
    }
  });
}

/**
 * 关闭购买弹窗的全局方法
 */
export function closePurchaseModal() {
  if (globalSetModalState) {
    globalSetModalState(prev => ({
      ...prev,
      isOpen: false,
    }));
  }
}

/**
 * 购买弹窗容器组件 - 需要在应用根部添加一次
 */
export function PurchaseModalContainer() {
  const [modalState, setModalState] = useState(globalModalState);
  const t = useTranslations('purchase');

  // 注册全局状态更新函数
  React.useEffect(() => {
    globalSetModalState = setModalState;
    return () => {
      globalSetModalState = null;
    };
  }, []);

  const [selectedPlan, setSelectedPlan] = useState<'pro' | 'pro-plus'>(
    modalState.options.defaultPlan || 'pro-plus'
  );
  const [selectedBilling, setSelectedBilling] = useState<'monthly' | 'yearly'>(
    modalState.options.defaultBilling || 'yearly'
  );
  const [isLoading, setIsLoading] = useState(false);

  // 重置状态当弹窗打开时
  React.useEffect(() => {
    if (modalState.isOpen) {
      setSelectedPlan(modalState.options.defaultPlan || 'pro-plus');
      setSelectedBilling(modalState.options.defaultBilling || 'yearly');
      setIsLoading(false);
    }
  }, [modalState.isOpen, modalState.options]);

  const getPlanConfig = (t: ReturnType<typeof useTranslations>) => ({
    pro: {
      id: 'pro' as const,
      name: t('pro'),
      monthly: {
        price: t('pricing.proMonthly'),
        originalPrice: t('pricing.proBilledMonthly'),
      },
      yearly: {
        price: t('pricing.proYearly'),
        originalPrice: t('pricing.proBilledYearly'),
      },
      features: [
        t('features.powerfulEditingTools'),
        t('features.aiTools'),
        t('features.aiPortraitEditing'),
        t('features.removeWatermark'),
        t('features.aiSlidesGenerating'),
        t('features.storage2G'),
      ],
    },
    'pro-plus': {
      id: 'pro-plus' as const,
      name: t('proPlus'),
      monthly: {
        price: t('pricing.proPlusMonthly'),
        originalPrice: t('pricing.proPlusBilledMonthly'),
      },
      yearly: {
        price: t('pricing.proPlusYearly'),
        originalPrice: t('pricing.proPlusBilledYearly'),
      },
      features: [
        t('features.powerfulEditingTools'),
        t('features.aiTools'),
        t('features.aiPortraitEditing'),
        t('features.removeWatermark'),
        t('features.aiSlidesGenerating'),
        t('features.storage10G'),
      ],
    },
  });

  const PLANS_CONFIG = getPlanConfig(t);

  const handleClose = () => {
    setModalState(prev => ({ ...prev, isOpen: false }));
    if (modalState.resolve) {
      modalState.resolve(null);
    }
  };

  const handlePurchase = async () => {
    setIsLoading(true);

    try {
      const planConfig = PLANS_CONFIG[selectedPlan];
      const billingConfig = planConfig[selectedBilling];

      const purchaseData = {
        id: planConfig.id,
        name: planConfig.name,
        price: billingConfig.price,
        billing: selectedBilling,
      };

      if (modalState.options.onPurchase) {
        await modalState.options.onPurchase(purchaseData);
      }

      if (modalState.resolve) {
        modalState.resolve(purchaseData);
      }

      handleClose();
    } catch (error) {
      console.error('Purchase failed:', error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Dialog open={modalState.isOpen} onOpenChange={handleClose}>
      <DialogContent className='max-w-[1005px] max-h-[90vh] overflow-y-auto overflow-x-hidden p-0 bg-[#111111] rounded-[40px] border-0 shadow-[0px_4px_24.4px_0px_rgba(0,0,0,0.25)]'>
        {/* Desktop Layout */}
        <div className='hidden md:flex relative w-full'>
          {/* Left Side - Purchase Options */}
          <div className='flex-shrink-0 w-[462px] bg-[#111111] p-12 text-white flex flex-col justify-between'>
            <div className='space-y-[18px]'>
              {/* Header with down arrow */}
              <div className='relative'>
                <h2
                  className='text-2xl font-bold leading-8 text-white'
                  style={{
                    fontFamily: 'Geologica, sans-serif',
                    fontVariationSettings: "'CRSV' 0, 'SHRP' 0",
                  }}
                >
                  Upgrade to
                  <br />
                  PixPretty Premium
                </h2>
                <div className='absolute top-8 right-0 w-6 h-6 flex items-center justify-center'>
                  <ChevronDown className='w-4 h-4 text-white' strokeWidth={2} />
                </div>
              </div>

              {/* Billing Toggle */}
              <div className='flex rounded-xl overflow-hidden'>
                <button
                  onClick={() => setSelectedBilling('monthly')}
                  className={cn(
                    'px-12 py-2 text-base font-light border transition-colors',
                    "font-['Geologica',sans-serif]",
                    selectedBilling === 'monthly'
                      ? 'bg-white text-[#111111] rounded-l-xl border-[rgba(17,17,17,0.4)]'
                      : 'bg-transparent text-white border-white rounded-l-xl'
                  )}
                  style={{ fontVariationSettings: "'CRSV' 0, 'SHRP' 0" }}
                >
                  Monthly
                </button>
                <button
                  onClick={() => setSelectedBilling('yearly')}
                  className={cn(
                    'px-14 py-2 text-base font-light border transition-colors relative',
                    "font-['Geologica',sans-serif]",
                    selectedBilling === 'yearly'
                      ? 'bg-[rgba(17,17,17,0.4)] text-white border-white rounded-r-xl'
                      : 'bg-transparent text-white border-white rounded-r-xl'
                  )}
                  style={{ fontVariationSettings: "'CRSV' 0, 'SHRP' 0" }}
                >
                  Yearly
                  <span
                    className='absolute -top-1 right-2 text-xs text-[#FFEE00] font-bold'
                    style={{ fontVariationSettings: "'CRSV' 0, 'SHRP' 0" }}
                  >
                    Save 25%
                  </span>
                </button>
              </div>

              {/* Plan Cards */}
              <div className='space-y-3'>
                {/* Pro+ Plan */}
                <div
                  className={cn(
                    'p-4 rounded-xl border cursor-pointer transition-all',
                    selectedPlan === 'pro-plus'
                      ? 'bg-[rgba(255,238,0,0.03)] border-[#FFEE00]'
                      : 'bg-[#1f1f1f] border-[#383838]'
                  )}
                  onClick={() => setSelectedPlan('pro-plus')}
                >
                  <div className='flex justify-between items-center'>
                    <div>
                      <h3
                        className="text-xl font-bold text-white font-[\'Geologica\',sans-serif]"
                        style={{ fontVariationSettings: "'CRSV' 0, 'SHRP' 0" }}
                      >
                        Pro+
                      </h3>
                      <p
                        className="text-sm font-light text-white font-[\'Geologica\',sans-serif]"
                        style={{ fontVariationSettings: "'CRSV' 0, 'SHRP' 0" }}
                      >
                        {selectedBilling === 'monthly'
                          ? '$39.99 billed monthly.'
                          : '$8.99 billed yearly.'}
                      </p>
                    </div>
                    <div
                      className="text-lg font-light text-white font-[\'Geologica\',sans-serif]"
                      style={{ fontVariationSettings: "'CRSV' 0, 'SHRP' 0" }}
                    >
                      {selectedBilling === 'monthly'
                        ? 'US$8.99/mo'
                        : 'US$8.99/mo'}
                    </div>
                  </div>
                </div>

                {/* Pro Plan */}
                <div
                  className={cn(
                    'p-4 rounded-xl border cursor-pointer transition-all',
                    selectedPlan === 'pro'
                      ? 'bg-[rgba(255,238,0,0.03)] border-[#FFEE00]'
                      : 'bg-[#1f1f1f] border-[#383838]'
                  )}
                  onClick={() => setSelectedPlan('pro')}
                >
                  <div className='flex justify-between items-center'>
                    <div>
                      <h3
                        className="text-xl font-bold text-white font-[\'Geologica\',sans-serif]"
                        style={{ fontVariationSettings: "'CRSV' 0, 'SHRP' 0" }}
                      >
                        Pro
                      </h3>
                      <p
                        className="text-sm font-light text-white font-[\'Geologica\',sans-serif]"
                        style={{ fontVariationSettings: "'CRSV' 0, 'SHRP' 0" }}
                      >
                        {selectedBilling === 'monthly'
                          ? '$16.9 billed monthly.'
                          : '$4.99 billed yearly.'}
                      </p>
                    </div>
                    <div
                      className="text-lg font-light text-white font-[\'Geologica\',sans-serif]"
                      style={{ fontVariationSettings: "'CRSV' 0, 'SHRP' 0" }}
                    >
                      {selectedBilling === 'monthly'
                        ? 'US$4.99/mo'
                        : 'US$4.99/mo'}
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Purchase Button */}
            <div className='space-y-2'>
              <Button
                onClick={handlePurchase}
                disabled={isLoading}
                className='w-full bg-[#FFEE00] hover:bg-[#FFEE00]/90 text-[#111111] font-light text-lg py-3 rounded-[98px] border border-[rgba(255,255,255,0.2)] shadow-[0px_4px_20.2px_0px_rgba(0,0,0,0.25)]'
                style={{
                  fontFamily: 'Geologica, sans-serif',
                  fontVariationSettings: "'CRSV' 0, 'SHRP' 0",
                }}
              >
                {isLoading ? t('processing') : 'Buy Now'}
              </Button>
              <div className='text-sm font-light text-[rgba(255,255,255,0.7)] text-center'>
                <p
                  className="font-[\'Geologica\',sans-serif]"
                  style={{ fontVariationSettings: "'CRSV' 0, 'SHRP' 0" }}
                >
                  Not sure yet? Check out our{' '}
                  <span className='text-[#2170e5]'>Pricing Page</span> for
                  details.
                </p>
              </div>
            </div>
          </div>

          {/* Right Side - Features */}
          <div className='flex-1 min-w-0 bg-white relative overflow-hidden rounded-r-[40px]'>
            {/* Product Preview Area */}
            <div className='relative h-[204px] overflow-hidden'>
              {/* Background cosmetics images */}
              <div className='absolute inset-0 bg-gradient-to-r from-orange-200 via-orange-100 to-yellow-100'>
                {/* Placeholder for cosmetics images */}
                <div className='absolute inset-0 flex items-center justify-center'>
                  <div className="w-full h-full bg-[url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTQzIiBoZWlnaHQ9IjIwNCIgdmlld0JveD0iMCAwIDU0MyAyMDQiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSI1NDMiIGhlaWdodD0iMjA0IiBmaWxsPSJ1cmwoI3BhaW50MF9saW5lYXJfMF8xKSIvPgo8ZGVmcz4KPGxpbmVhckdyYWRpZW50IGlkPSJwYWludDBfbGluZWFyXzBfMSIgeDE9IjAiIHkxPSIxMDIiIHgyPSI1NDMiIHkyPSIxMDIiIGdyYWRpZW50VW5pdHM9InVzZXJTcGFjZU9uVXNlIj4KPHN0b3Agc3RvcC1jb2xvcj0iI0ZGRTRCNSIvPgo8c3RvcCBvZmZzZXQ9IjEiIHN0b3AtY29sb3I9IiNGRkY5RTAiLz4KPC9saW5lYXJHcmFkaWVudD4KPC9kZWZzPgo8L3N2Zz4K')] bg-cover bg-center opacity-60" />
                </div>
              </div>
              {/* Divider line */}
              <div className='absolute left-1/2 top-0 bottom-0 w-px bg-gray-400/20 transform -translate-x-1/2' />
              {/* Control button */}
              <div className='absolute top-1/2 left-1/2 w-5 h-5 transform -translate-x-1/2 -translate-y-1/2'>
                <div className='w-full h-full bg-white/30 rounded-md flex items-center justify-center'>
                  <div className='w-2 h-2 bg-gray-600 rounded-full' />
                </div>
              </div>
            </div>

            {/* Features List */}
            <div className='px-11 py-5 space-y-5'>
              <h3
                className="text-base font-bold text-[#111111] font-[\'Geologica\',sans-serif]"
                style={{ fontVariationSettings: "'CRSV' 0, 'SHRP' 0" }}
              >
                Features
              </h3>

              {/* Features and Plan Comparison */}
              <div className='flex gap-4 overflow-x-hidden'>
                {/* Features Column */}
                <div className='flex-1 min-w-0 space-y-3'>
                  {[
                    '100+ powerful editing tools',
                    'Al tools: Al enlarger,1-tap enhance,magic eraser. background remover',
                    'Al portrait editing: Al skin retouch,face unblur, old photo restorer',
                    'Remove the watermark of Fotor logo',
                    'Al slides generating',
                    'Al portrait editing: Al skin retouch,face unblur, old photo restorer',
                  ].map((feature, index) => (
                    <div
                      key={index}
                      className="text-xs font-light text-[#111111] leading-relaxed font-[\'Geologica\',sans-serif]"
                      style={{ fontVariationSettings: "'CRSV' 0, 'SHRP' 0" }}
                    >
                      {feature}
                    </div>
                  ))}
                </div>

                {/* Pro Column */}
                <div className='flex-shrink-0 w-20'>
                  <h4
                    className="text-base font-bold text-center text-[#111111] mb-5 font-[\'Geologica\',sans-serif]"
                    style={{ fontVariationSettings: "'CRSV' 0, 'SHRP' 0" }}
                  >
                    Pro
                  </h4>
                  <div className='space-y-3 flex flex-col items-center'>
                    {[1, 2, 3, 4].map((_, index) => (
                      <div
                        key={index}
                        className='flex justify-center items-center h-[34px] w-full'
                      >
                        <X
                          className='w-6 h-6 text-[#111111]'
                          strokeWidth={1.5}
                        />
                      </div>
                    ))}
                    <div className='flex justify-center items-center h-[34px] w-full'>
                      <span
                        className="text-base font-bold text-[#111111] font-[\'Geologica\',sans-serif]"
                        style={{ fontVariationSettings: "'CRSV' 0, 'SHRP' 0" }}
                      >
                        2G
                      </span>
                    </div>
                    <div className='flex justify-center items-center h-[34px] w-full'>
                      <X className='w-6 h-6 text-[#111111]' strokeWidth={1.5} />
                    </div>
                  </div>
                </div>

                {/* Pro+ Column */}
                <div className='flex-shrink-0 w-20 bg-[rgba(255,238,0,0.08)] border border-[#FFB200] rounded-xl pt-4 pb-[22px]'>
                  <h4
                    className="text-base font-bold text-center text-[#111111] mb-[18px] font-[\'Geologica\',sans-serif]"
                    style={{ fontVariationSettings: "'CRSV' 0, 'SHRP' 0" }}
                  >
                    Pro+
                  </h4>
                  <div className='space-y-3 flex flex-col items-center'>
                    {[1, 2, 3, 4].map((_, index) => (
                      <div
                        key={index}
                        className='flex justify-center items-center h-[34px] w-full'
                      >
                        <Check
                          className='w-6 h-6 text-[#111111]'
                          strokeWidth={2}
                        />
                      </div>
                    ))}
                    <div className='flex justify-center items-center h-[34px] w-full'>
                      <span
                        className="text-base font-bold text-[#111111] font-[\'Geologica\',sans-serif]"
                        style={{ fontVariationSettings: "'CRSV' 0, 'SHRP' 0" }}
                      >
                        10G
                      </span>
                    </div>
                    <div className='flex justify-center items-center h-[34px] w-full'>
                      <Check
                        className='w-6 h-6 text-[#111111]'
                        strokeWidth={2}
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Scroll bar */}
            <div className='absolute right-6 top-[194px] w-1.5 h-[346px] bg-[rgba(17,17,17,0.08)] rounded-[13px]'>
              <div className='w-full h-20 bg-[rgba(17,17,17,0.2)] rounded-[13px]' />
            </div>
          </div>
        </div>

        {/* Mobile Layout */}
        <div className='md:hidden bg-[#111111] text-white rounded-3xl p-5 w-full max-w-[358px] mx-auto overflow-x-hidden'>
          <div className='space-y-10'>
            {/* Header */}
            <div className='space-y-5'>
              <div className='relative'>
                <h2
                  className="text-2xl font-bold leading-8 text-white font-[\'Geologica\',sans-serif]"
                  style={{ fontVariationSettings: "'CRSV' 0, 'SHRP' 0" }}
                >
                  Upgrade to
                  <br />
                  PixPretty Premium
                </h2>
                <div className='absolute top-0 right-0 w-6 h-6'>
                  <div className='w-full h-full border border-white rounded-md flex items-center justify-center'>
                    <X className='w-4 h-4 text-white' strokeWidth={1.5} />
                  </div>
                </div>
              </div>

              {/* Billing Toggle */}
              <div className='flex rounded-xl overflow-hidden'>
                <button
                  onClick={() => setSelectedBilling('monthly')}
                  className={cn(
                    'px-12 py-2 text-base font-light transition-colors',
                    "font-['Geologica',sans-serif]",
                    selectedBilling === 'monthly'
                      ? 'bg-white text-[#111111] rounded-l-xl'
                      : 'bg-[#1f1f1f] text-white border border-white rounded-l-xl'
                  )}
                  style={{ fontVariationSettings: "'CRSV' 0, 'SHRP' 0" }}
                >
                  Monthly
                </button>
                <button
                  onClick={() => setSelectedBilling('yearly')}
                  className={cn(
                    'px-14 py-3 text-base font-light transition-colors relative',
                    "font-['Geologica',sans-serif]",
                    selectedBilling === 'yearly'
                      ? 'bg-[#1f1f1f] text-white border border-white rounded-r-xl'
                      : 'bg-transparent text-white border border-white rounded-r-xl'
                  )}
                  style={{ fontVariationSettings: "'CRSV' 0, 'SHRP' 0" }}
                >
                  Yearly
                  <span
                    className='absolute top-0 right-2 text-xs text-[#FFB200] font-bold text-shadow-[rgba(0,0,0,0.25)_0px_1px_1.2px]'
                    style={{ fontVariationSettings: "'CRSV' 0, 'SHRP' 0" }}
                  >
                    Save 25%
                  </span>
                </button>
              </div>

              {/* Plan Cards */}
              <div className='space-y-3'>
                {/* Pro Plan */}
                <div
                  className={cn(
                    'p-3 rounded-xl border cursor-pointer transition-all',
                    selectedPlan === 'pro'
                      ? 'bg-[rgba(255,238,0,0.03)] border-[#FFEE00]'
                      : 'bg-[#1f1f1f] border-[#383838]'
                  )}
                  onClick={() => setSelectedPlan('pro')}
                >
                  <div className='flex justify-between items-center'>
                    <div>
                      <h3
                        className="text-xl font-bold text-white font-[\'Geologica\',sans-serif]"
                        style={{ fontVariationSettings: "'CRSV' 0, 'SHRP' 0" }}
                      >
                        Pro
                      </h3>
                      <p
                        className="text-xs font-light text-white font-[\'Geologica\',sans-serif]"
                        style={{ fontVariationSettings: "'CRSV' 0, 'SHRP' 0" }}
                      >
                        $39.99 billed monthly.
                      </p>
                    </div>
                    <div
                      className="text-base font-light text-white font-[\'Geologica\',sans-serif]"
                      style={{ fontVariationSettings: "'CRSV' 0, 'SHRP' 0" }}
                    >
                      US$8.99/mo
                    </div>
                  </div>
                </div>

                {/* Pro+ Plan */}
                <div
                  className={cn(
                    'p-4 rounded-xl border cursor-pointer transition-all',
                    selectedPlan === 'pro-plus'
                      ? 'bg-[rgba(255,238,0,0.03)] border-[#FFEE00]'
                      : 'bg-[#1f1f1f] border-[#383838]'
                  )}
                  onClick={() => setSelectedPlan('pro-plus')}
                >
                  <div className='flex justify-between items-center'>
                    <div>
                      <h3
                        className="text-xl font-bold text-white font-[\'Geologica\',sans-serif]"
                        style={{ fontVariationSettings: "'CRSV' 0, 'SHRP' 0" }}
                      >
                        Pro+
                      </h3>
                      <p
                        className="text-xs font-light text-white font-[\'Geologica\',sans-serif]"
                        style={{ fontVariationSettings: "'CRSV' 0, 'SHRP' 0" }}
                      >
                        $39.99 billed monthly.
                      </p>
                    </div>
                    <div
                      className="text-base font-light text-white font-[\'Geologica\',sans-serif]"
                      style={{ fontVariationSettings: "'CRSV' 0, 'SHRP' 0" }}
                    >
                      US$4.99/mo
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Purchase Button */}
            <div className='space-y-2'>
              <Button
                onClick={handlePurchase}
                disabled={isLoading}
                className='w-full bg-[#FFEE00] hover:bg-[#FFEE00]/90 text-[#111111] font-light py-3.5 rounded-[98px] border border-[#FFEE00] shadow-[0px_4px_20.2px_0px_rgba(0,0,0,0.25)]'
                style={{
                  fontFamily: 'Geologica, sans-serif',
                  fontVariationSettings: "'CRSV' 0, 'SHRP' 0",
                }}
              >
                {isLoading ? t('processing') : 'Buy Now'}
              </Button>
              <div className='text-sm font-light text-[rgba(255,255,255,0.7)] text-center space-y-0'>
                <p
                  className="font-[\'Geologica\',sans-serif] mb-0"
                  style={{ fontVariationSettings: "'CRSV' 0, 'SHRP' 0" }}
                >
                  Not sure yet?
                </p>
                <p
                  className="font-[\'Geologica\',sans-serif]"
                  style={{ fontVariationSettings: "'CRSV' 0, 'SHRP' 0" }}
                >
                  Check out our{' '}
                  <span className='text-[#2170e5]'>Pricing Page</span> for
                  details.
                </p>
              </div>
            </div>
          </div>

          {/* Features Table (Mobile) */}
          <div className='mt-10 bg-white h-[252px] rounded-xl relative'>
            <div className='p-3 space-y-5'>
              <h3
                className="text-base font-bold text-[#111111] font-[\'Geologica\',sans-serif]"
                style={{ fontVariationSettings: "'CRSV' 0, 'SHRP' 0" }}
              >
                Features
              </h3>

              {/* Compact Features and Plan Comparison */}
              <div className='flex gap-2 overflow-x-hidden'>
                {/* Features Column */}
                <div className='flex-1 min-w-0 space-y-3'>
                  {[
                    '100+ powerful editing tools',
                    'Al tools: Al enlarger,1-tap enhance,magic eraser. background remover',
                    'Al portrait editing: Al skin retouch,face unblur, old photo restorer',
                    'Remove the watermark of Fotor logo',
                    'Al slides generating',
                  ].map((feature, index) => (
                    <div
                      key={index}
                      className="text-xs font-light text-[#111111] leading-relaxed font-[\'Geologica\',sans-serif]"
                      style={{ fontVariationSettings: "'CRSV' 0, 'SHRP' 0" }}
                    >
                      {feature}
                    </div>
                  ))}
                </div>

                {/* Pro Column */}
                <div className='flex-shrink-0 w-16'>
                  <h4
                    className="text-sm font-bold text-center text-[#111111] mb-5 font-[\'Geologica\',sans-serif]"
                    style={{ fontVariationSettings: "'CRSV' 0, 'SHRP' 0" }}
                  >
                    Pro
                  </h4>
                  <div className='space-y-3 flex flex-col items-center'>
                    {[1, 2, 3, 4].map((_, index) => (
                      <div
                        key={index}
                        className='flex justify-center items-center h-[34px] w-full'
                      >
                        <X
                          className='w-6 h-6 text-[#111111]'
                          strokeWidth={1.5}
                        />
                      </div>
                    ))}
                    <div className='flex justify-center items-center h-[34px] w-full'>
                      <span
                        className="text-base font-bold text-[#111111] font-[\'Geologica\',sans-serif]"
                        style={{ fontVariationSettings: "'CRSV' 0, 'SHRP' 0" }}
                      >
                        2G
                      </span>
                    </div>
                  </div>
                </div>

                {/* Pro+ Column */}
                <div className='flex-shrink-0 w-16 bg-[rgba(255,238,0,0.08)] border border-[#FFB200] rounded-xl pt-4 pb-[22px]'>
                  <h4
                    className="text-sm font-bold text-center text-[#111111] mb-[18px] font-[\'Geologica\',sans-serif]"
                    style={{ fontVariationSettings: "'CRSV' 0, 'SHRP' 0" }}
                  >
                    Pro+
                  </h4>
                  <div className='space-y-3 flex flex-col items-center'>
                    {[1, 2, 3, 4].map((_, index) => (
                      <div
                        key={index}
                        className='flex justify-center items-center h-[34px] w-full'
                      >
                        <Check
                          className='w-6 h-6 text-[#111111]'
                          strokeWidth={2}
                        />
                      </div>
                    ))}
                    <div className='flex justify-center items-center h-[34px] w-full'>
                      <span
                        className="text-base font-bold text-[#111111] font-[\'Geologica\',sans-serif]"
                        style={{ fontVariationSettings: "'CRSV' 0, 'SHRP' 0" }}
                      >
                        10G
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Scroll bar */}
            <div className='absolute right-3 top-4 w-1.5 h-[346px] bg-[rgba(17,17,17,0.08)] rounded-[13px]'>
              <div className='w-full h-20 bg-[rgba(17,17,17,0.2)] rounded-[13px]' />
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
