import React from 'react';

interface IconProps {
  className?: string;
  size?: number;
}

const EraseIcon: React.FC<IconProps> = ({ className, size = 24 }) => (
  <svg
    width={size}
    height={size}
    viewBox='0 0 24 24'
    fill='none'
    className={className}
  >
    <circle cx='12' cy='12' r='10' strokeWidth='1.5' />
    <path d='M8 12.0049H16' strokeWidth='1.5' strokeLinecap='round' />
    <path d='M12 8.00488L12 16.0049' strokeWidth='1.5' strokeLinecap='round' />
  </svg>
);

export default EraseIcon;
