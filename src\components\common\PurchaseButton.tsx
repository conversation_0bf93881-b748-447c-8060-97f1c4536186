'use client';

import React from 'react';
import { Button } from '@/components/ui/Button';
import { showPurchaseModal } from './PurchaseModalSimple';
interface PurchaseButtonProps extends React.ComponentProps<typeof Button> {
  plan?: 'pro' | 'pro-plus';
  billing?: 'monthly' | 'yearly';
  onSuccess?: (plan: { id: 'pro' | 'pro-plus'; name: string; price: string; billing: 'monthly' | 'yearly'; }) => void;
}

/**
 * 快速购买按钮 - 封装了showPurchaseModal的按钮组件
 */
export function PurchaseButton({ 
  children = "升级", 
  plan = 'pro-plus',
  billing = 'yearly',
  onSuccess,
  ...buttonProps 
}: PurchaseButtonProps) {
  
  const handleClick = () => {
    showPurchaseModal({
      defaultPlan: plan,
      defaultBilling: billing,
      onPurchase: async (purchasedPlan) => {
        // 这里可以集成真实的购买逻辑
        console.log('购买:', purchasedPlan);
        onSuccess?.(purchasedPlan);
      },
    });
  };

  return (
    <Button onClick={handleClick} {...buttonProps}>
      {children}
    </Button>
  );
}

interface FeatureLockProps {
  featureName: string;
  requiredPlan?: 'pro' | 'pro-plus';
}

/**
 * 功能限制组件 - 当用户使用受限功能时显示
 */
export function FeatureLock({ 
  featureName, 
  requiredPlan = 'pro-plus' 
}: FeatureLockProps) {
  const handleUnlock = () => {
    showPurchaseModal({
      defaultPlan: requiredPlan,
      onPurchase: async (plan) => {
        console.log(`解锁 ${featureName} 功能:`, plan);
        // 这里可以刷新页面或更新用户状态
      },
    });
  };

  return (
    <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-sm font-medium text-yellow-800">
            🔒 {featureName} 功能已锁定
          </h3>
          <p className="text-sm text-yellow-700 mt-1">
            升级到 {requiredPlan === 'pro' ? 'Pro' : 'Pro+'} 计划解锁此功能
          </p>
        </div>
        <Button
          size="sm"
          onClick={handleUnlock}
          className="bg-yellow-400 hover:bg-yellow-500 text-yellow-900"
        >
          立即解锁
        </Button>
      </div>
    </div>
  );
}
