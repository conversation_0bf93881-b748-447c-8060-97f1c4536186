import { DateRange } from 'react-day-picker';

export function dateRangeHandler(
  range: DateRange | undefined,
  date: DateRange | undefined
) {
  if (!range) {
    return undefined;
  }
  // 当前选择了起止日期
  if (date?.from && date?.to) {
    if (range?.from) {
      if (range?.from.getTime() < date.from.getTime()) {
        // 选择的日期在当前日期之前
        return { from: range.from, to: undefined };
      } else {
        // 选择的日期在当前日期之后
        return { from: range.to, to: undefined };
      }
    }
  }

  // 当前没有选中任何日期
  if (!date?.from && !date?.to) {
    return { from: range.from, to: undefined };
  }

  // 只选中了 from
  if (date?.from && !date?.to) {
    return { from: range.from, to: range.to };
  }
}
