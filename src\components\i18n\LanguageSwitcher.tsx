'use client';

import { useLocale } from 'next-intl';
import { useState, useTransition } from 'react';
import { ChevronDown, Check } from 'lucide-react';
import { Locale, localeConfig } from '@/i18n/config';
import { setUserLocale } from '@/services/locale';
import Image from 'next/image';

// 支持的语言配置
const locales = Object.entries(localeConfig).map(([code, config]) => ({
  code: code as Locale,
  name: config.name,
  flag: config.flag,
}));

interface LanguageSwitcherProps {
  variant?: 'select' | 'button';
  className?: string;
}

export function LanguageSwitcher({
  variant = 'select',
  className = '',
}: LanguageSwitcherProps) {
  const locale = useLocale() as Locale;
  const [isPending, startTransition] = useTransition();
  const [isOpen, setIsOpen] = useState(false);

  const currentLocale = locales.find(l => l.code === locale) || locales[0];

  const handleLanguageChange = (newLocale: Locale) => {
    if (newLocale === locale) return;

    startTransition(() => {
      // 使用服务端函数设置语言
      setUserLocale(newLocale);
    });

    setIsOpen(false);
  };

  if (variant === 'select') {
    return (
      <div className={`relative ${className}`}>
        <button
          onClick={() => setIsOpen(!isOpen)}
          disabled={isPending}
          className='flex items-center gap-2 px-3 py-1 text-sm rounded-md hover:bg-[#F0F0F0] focus:bg-[#F0F0F0] disabled:opacity-50'
        >
          <Image
            src='/apps/icons/language.svg'
            alt='language'
            width={24}
            height={24}
          />
          <span>{currentLocale.name}</span>
          <ChevronDown
            className={`w-4 h-4 transition-transform ${isOpen ? 'rotate-180' : ''}`}
          />
        </button>

        {isOpen && (
          <div className='absolute top-full start-0 mt-1 w-40 bg-white border border-[#e7e7e7] rounded-xl shadow-[0px_10px_32px_0px_rgba(220,223,228,0.08),0px_8px_16px_0px_rgba(220,223,228,0.12),0px_7px_14px_0px_rgba(220,223,228,0.16)] z-50 p-2'>
            <div className='flex flex-col gap-1'>
              {locales.map(loc => (
                <button
                  key={loc.code}
                  onClick={() => handleLanguageChange(loc.code)}
                  disabled={isPending}
                  className={`w-full flex items-center gap-1 px-3 py-2 text-base text-left rounded-lg disabled:opacity-50 transition-colors ${
                    loc.code === locale
                      ? 'bg-[rgba(255,204,3,0.3)] text-[#000000]'
                      : 'text-[#000000] hover:bg-[#f0f0f0]'
                  }`}
                >
                  <div className='flex items-center justify-center w-4 h-4 shrink-0'>
                    <Check
                      className={`w-4 h-4 text-[#121212] transition-opacity ${
                        loc.code === locale ? 'opacity-100' : 'opacity-0'
                      }`}
                    />
                  </div>
                  <span className='font-normal leading-[1.5]'>{loc.name}</span>
                </button>
              ))}
            </div>
          </div>
        )}
      </div>
    );
  }

  // Button variant
  return (
    <div className={`flex gap-2 ${className}`}>
      {locales.map(loc => (
        <button
          key={loc.code}
          onClick={() => handleLanguageChange(loc.code)}
          disabled={isPending}
          className={`px-3 py-1 text-sm rounded-md transition-colors disabled:opacity-50 ${
            loc.code === locale
              ? 'bg-blue-600 text-white'
              : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
          }`}
        >
          {loc.flag} {loc.name}
        </button>
      ))}
    </div>
  );
}
