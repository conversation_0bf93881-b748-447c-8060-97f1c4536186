import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const imageUrl = searchParams.get('url');

    if (!imageUrl) {
      return NextResponse.json({ error: '缺少图片URL参数' }, { status: 400 });
    }

    // 基本URL验证
    let validUrl: URL;
    try {
      validUrl = new URL(imageUrl);
    } catch {
      return NextResponse.json({ error: '无效的URL格式' }, { status: 400 });
    }

    // 只允许http/https协议
    if (!['http:', 'https:'].includes(validUrl.protocol)) {
      return NextResponse.json(
        { error: '只支持HTTP/HTTPS协议' },
        { status: 400 }
      );
    }

    // 获取图片
    const response = await fetch(imageUrl, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (compatible; ImageProxy/1.0)',
        Accept: 'image/*',
      },
      signal: AbortSignal.timeout(15000), // 15秒超时
      redirect: 'follow',
    });

    if (!response.ok) {
      return NextResponse.json(
        { error: `无法获取图片: ${response.status}` },
        { status: response.status }
      );
    }

    const contentType = response.headers.get('content-type') || 'image/png';
    const imageBuffer = await response.arrayBuffer();

    // 简单的图片类型检查
    if (!contentType.startsWith('image/') && imageBuffer.byteLength > 0) {
      // 检查常见的图片文件头
      const uint8Array = new Uint8Array(imageBuffer.slice(0, 8));
      const isPNG = uint8Array[0] === 0x89 && uint8Array[1] === 0x50;
      const isJPEG = uint8Array[0] === 0xff && uint8Array[1] === 0xd8;
      const isGIF = uint8Array[0] === 0x47 && uint8Array[1] === 0x49;
      const isWebP = uint8Array[0] === 0x52 && uint8Array[1] === 0x49;

      if (!(isPNG || isJPEG || isGIF || isWebP)) {
        return NextResponse.json(
          { error: 'URL不是有效的图片资源' },
          { status: 400 }
        );
      }
    }

    // 返回图片数据
    return new NextResponse(imageBuffer, {
      status: 200,
      headers: {
        'Content-Type': contentType.startsWith('image/')
          ? contentType
          : 'image/png',
        'Access-Control-Allow-Origin': '*',
        'Cache-Control': 'public, max-age=3600',
        'Content-Length': imageBuffer.byteLength.toString(),
      },
    });
  } catch (error) {
    console.error('图片代理错误:', error);
    return NextResponse.json({ error: '服务器内部错误' }, { status: 500 });
  }
}

export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
    },
  });
}
