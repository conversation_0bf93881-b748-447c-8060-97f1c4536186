import Image from 'next/image';
import { Button } from '@/components/ui/Button';
import { Dialog, DialogContent, DialogTitle } from '@/components/ui/Dialog';
import { Upload } from 'lucide-react';
import { useTranslations } from 'next-intl';
import type { ImageState } from '@/store/imageStore';
import { VisuallyHidden } from '@/components/ui/VisuallyHidden';
import { MAX_SINGLE_IMAGES_LIMIT } from '@/config/constants';
import { useTips } from '@/components/ui/Tips';

interface ImageHistoryBarProps {
  images: ImageState[];
  selectedImageIds: Set<string>;
  isLoadingApi: boolean;
  currentImageId: string | null;
  open: () => void;
  handleSelectImage: (imageId: string) => Promise<ImageState | undefined>;
  showDeleteConfirmation: (imageId: string) => void;
  deleteDialogOpen: boolean;
  setDeleteDialogOpen: (open: boolean) => void;
  confirmDeleteImage: () => void;
  cancelDeleteImage: () => void;
  imagesCount: number;
}

export const ImageHistoryBar = ({
  images,
  selectedImageIds,
  isLoadingApi,
  currentImageId,
  open,
  handleSelectImage,
  showDeleteConfirmation,
  deleteDialogOpen,
  setDeleteDialogOpen,
  confirmDeleteImage,
  cancelDeleteImage,
  imagesCount,
}: ImageHistoryBarProps) => {
  const common = useTranslations('common');
  const messages = useTranslations('messages');
  const { showTips } = useTips();

  return (
    <>
      <div className='ps-6 pt-6 pb-6'>
        {/* 顶部图片历史记录栏 */}
        <div className='max-w-7xl'>
          <div className='flex items-center gap-2'>
            {/* 上传按钮 */}
            <div
              onClick={
                !isLoadingApi
                  ? () => {
                      // 检查图片数量限制
                      if (imagesCount >= MAX_SINGLE_IMAGES_LIMIT) {
                        showTips(
                          'error',
                          messages('singleImage.imagesExceedLimit', {
                            count: MAX_SINGLE_IMAGES_LIMIT,
                          }),
                          4000
                        );
                        return;
                      }

                      open();
                    }
                  : undefined
              }
              className={`w-20 h-20 border-1 rounded-lg transition-all duration-200 flex flex-col items-center justify-center border-gray-300 hover:border-gray-400 hover:bg-gray-50 cursor-pointer'
                      }
                    `}
            >
              <>
                <Upload className='w-4 h-4 mb-1' />
                {/* <Plus className='w-4 h-4 text-gray-400 mb-1' /> */}
                <span className='text-[12px] text-[#121212] text-center leading-[1.5]'>
                  {common('uploadImage')}
                </span>
              </>
            </div>
            {/* 历史记录列表 */}
            {/* 此处 pt-[10px] -mt-[10px]为解决垂直方向被裁剪的问题 */}
            <div
              className='w-[calc(100vw-7rem)] flex overflow-x-auto whitespace-nowrap gap-2 pe-1 pt-[10px] -mt-[10px]'
              style={{
                WebkitOverflowScrolling: 'touch',
                scrollSnapType: 'x mandatory',
                scrollPadding: '0 1rem',
                // 隐藏滚动条但保持功能
                msOverflowStyle: 'none',
                scrollbarWidth: 'none',
              }}
            >
              {images.map(image => {
                const isCurrentlyProcessingThisImage =
                  isLoadingApi && image.id === currentImageId;
                const isClickable = !isLoadingApi;
                const isSelected = selectedImageIds.has(image.id);

                return (
                  <div
                    key={image.id}
                    className={`relative group rounded-lg transition-all duration-200 w-20 h-20 flex-shrink-0
                      ${
                        isCurrentlyProcessingThisImage
                          ? 'border-4 border-[#ffffff] border-solid'
                          : isSelected
                            ? 'border-2 border-[#ffcc03] border-solid p-0.5'
                            : 'border border-[#e7e7e7] border-solid'
                      }
                      ${
                        isClickable
                          ? 'cursor-pointer hover:border-gray-300'
                          : 'cursor-default'
                      }
                      ${
                        isLoadingApi && !isCurrentlyProcessingThisImage
                          ? 'opacity-50 pointer-events-none'
                          : ''
                      }
                    `}
                    onClick={
                      isClickable
                        ? () => handleSelectImage(image.id)
                        : undefined
                    }
                  >
                    <Image
                      src={image.previewUrl}
                      alt={image.name}
                      className='w-full h-full object-cover rounded-md'
                      width={80}
                      height={80}
                    />
                    {/* 当前处理中的图片遮罩 */}
                    {isCurrentlyProcessingThisImage && (
                      <div className='absolute inset-0 bg-black/30 bg-opacity-30 flex items-center justify-center rounded-md'>
                        <svg
                          className='animate-spin h-5 w-5 text-white'
                          xmlns='http://www.w3.org/2000/svg'
                          fill='none'
                          viewBox='0 0 24 24'
                        >
                          <circle
                            className='opacity-25'
                            cx='12'
                            cy='12'
                            r='10'
                            stroke='currentColor'
                            strokeWidth='4'
                          ></circle>
                          <path
                            className='opacity-75'
                            fill='currentColor'
                            d='M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z'
                          ></path>
                        </svg>
                      </div>
                    )}
                    {/* 删除按钮 */}
                    {!isLoadingApi && (
                      <Button
                        variant='ghost'
                        size='icon'
                        className='absolute -top-2 -end-2 w-6 h-6 bg-[#121212]/60 text-white rounded-full flex items-center justify-center'
                        onClick={e => {
                          e.stopPropagation();
                          showDeleteConfirmation(image.id);
                        }}
                      >
                        <Image
                          src='/apps/icons/mDelete.svg'
                          alt='mDelete'
                          width={16}
                          height={16}
                        />
                      </Button>
                    )}
                  </div>
                );
              })}
            </div>
            {/* 历史图片列表 */}
          </div>
        </div>
      </div>

      {/* 删除确认弹窗 */}
      <Dialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <DialogContent className='w-[542px] bg-white rounded-2xl shadow-[0px_4px_8px_0px_rgba(19,19,20,0.5)] border-0 p-0 gap-0'>
          <VisuallyHidden>
            <DialogTitle>{messages('singleImage.confirmDelete')}</DialogTitle>
          </VisuallyHidden>
          {/* 顶部关闭按钮区域 */}
          <div className='h-10 relative rounded-t-2xl w-full'></div>

          {/* 主要内容区域 */}
          <div className='px-8 pb-8'>
            <div className='w-full flex flex-col'>
              <div className='flex-col justify-items-center'>
                {/* 问号图标 */}
                <Image
                  src='/apps/icons/dialogInfo.svg'
                  alt='dialogInfo'
                  width={64}
                  height={64}
                />
                <div className='mt-5'>
                  {/* 标题 */}
                  <p className='text-[#121212] text-[18px] font-medium leading-[1.5] text-center'>
                    {messages('singleImage.confirmDelete')}
                  </p>
                </div>
              </div>
              {/* 按钮区域 */}
              <div className='flex gap-3 w-full justify-between mt-8'>
                <Button
                  variant='outline'
                  onClick={cancelDeleteImage}
                  className='flex-1 border-[#e7e7e7] bg-white hover:bg-gray-50'
                >
                  {common('cancel')}
                </Button>
                <Button className='flex-1' onClick={confirmDeleteImage}>
                  {common('delete')}
                </Button>
              </div>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
};
